"""
Basic Phoenix setup and server launch script.
This script demonstrates how to start Phoenix and configure it for LLM evaluations.
"""

import os
import phoenix as px
from phoenix.evals import OpenAIModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_phoenix():
    """
    Set up and launch Phoenix for LLM evaluations.
    """
    print("🚀 Starting Phoenix...")
    
    # Launch Phoenix - this will start the web UI
    session = px.launch_app()
    
    print(f"✅ Phoenix is running at: {session.url}")
    print("📊 You can view the Phoenix UI in your browser")
    
    return session

def setup_evaluation_model():
    """
    Set up the evaluation model (OpenAI GPT-4).
    Make sure to set your OPENAI_API_KEY environment variable.
    """
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("⚠️  Warning: OPENAI_API_KEY not found in environment variables")
        print("Please set your OpenAI API key to run evaluations:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return None
    
    # Create evaluation model
    eval_model = OpenAIModel(
        model="gpt-4o",  # You can also use "gpt-4", "gpt-3.5-turbo", etc.
        temperature=0.0  # Low temperature for consistent evaluations
    )
    
    print("✅ Evaluation model (GPT-4) configured successfully")
    return eval_model

def main():
    """
    Main setup function.
    """
    print("🔧 Setting up Arize Phoenix for LLM Evaluations")
    print("=" * 50)
    
    # Start Phoenix
    session = setup_phoenix()
    
    # Setup evaluation model
    eval_model = setup_evaluation_model()
    
    print("\n📋 Next Steps:")
    print("1. Open the Phoenix UI in your browser")
    print("2. Run the evaluation examples:")
    print("   - python basic_evaluation_example.py")
    print("   - python custom_evaluation_example.py")
    print("   - python tracing_with_evaluation.py")
    print("   - python rag_evaluation_example.py")
    
    print("\n🔗 Useful Links:")
    print(f"   Phoenix UI: {session.url}")
    print("   Documentation: https://docs.arize.com/phoenix")
    
    return session, eval_model

if __name__ == "__main__":
    session, eval_model = main()
    
    # Keep the script running so Phoenix stays active
    print("\n⏳ Phoenix is running. Press Ctrl+C to stop.")
    try:
        input("Press Enter to stop Phoenix...")
    except KeyboardInterrupt:
        print("\n👋 Stopping Phoenix...")
    finally:
        print("✅ Phoenix stopped.")
