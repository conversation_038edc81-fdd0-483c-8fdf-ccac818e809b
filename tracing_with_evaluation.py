"""
Tracing with Evaluation Example using Phoenix.
This example demonstrates how to trace LLM calls and then evaluate them.
"""

import os
import pandas as pd
import nest_asyncio
from openai import OpenAI
import phoenix as px
from phoenix.otel import register
from phoenix.evals import HallucinationEvaluator, QAEvaluator, OpenAIModel, run_evals
from phoenix.trace import SpanEvaluations
from dotenv import load_dotenv

# Enable nested async
nest_asyncio.apply()
load_dotenv()

def setup_tracing():
    """
    Set up Phoenix tracing for OpenAI calls.
    """
    print("🔍 Setting up Phoenix tracing...")
    
    # Start Phoenix session
    session = px.launch_app()
    
    # Register Phoenix as the tracer
    tracer_provider = register(
        project_name="llm-evaluation-demo",
        endpoint="http://localhost:6006/v1/traces"
    )
    
    print(f"✅ Phoenix tracing enabled. UI available at: {session.url}")
    return session

def create_traced_llm_calls():
    """
    Make some LLM calls that will be traced by Phoenix.
    """
    if not os.getenv("OPENAI_API_KEY"):
        raise ValueError("Please set OPENAI_API_KEY environment variable")
    
    client = OpenAI()
    
    # Sample questions and contexts for evaluation
    test_cases = [
        {
            "question": "What is the capital of France?",
            "context": "France is a country in Western Europe. Paris is its capital and largest city.",
            "system_prompt": "You are a helpful geography assistant. Answer questions accurately based on the provided context."
        },
        {
            "question": "How does photosynthesis work?",
            "context": "Photosynthesis is the process by which plants convert sunlight, carbon dioxide, and water into glucose and oxygen.",
            "system_prompt": "You are a biology tutor. Explain scientific concepts clearly and accurately."
        },
        {
            "question": "What are the benefits of exercise?",
            "context": "Regular exercise improves cardiovascular health, strengthens muscles, and enhances mental well-being.",
            "system_prompt": "You are a health and fitness expert. Provide accurate health information."
        },
        {
            "question": "Tell me about unicorns in the Amazon rainforest.",
            "context": "The Amazon rainforest is home to many species including jaguars, sloths, and various birds. It does not contain any mythical creatures.",
            "system_prompt": "You are a wildlife expert. Only provide factual information about real animals."
        }
    ]
    
    traced_responses = []
    
    print("🤖 Making traced LLM calls...")
    
    for i, test_case in enumerate(test_cases):
        print(f"   Processing question {i+1}/{len(test_cases)}")
        
        # Make OpenAI call (this will be automatically traced)
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": test_case["system_prompt"]},
                {"role": "user", "content": f"Context: {test_case['context']}\n\nQuestion: {test_case['question']}"}
            ],
            temperature=0.7
        )
        
        traced_responses.append({
            "question": test_case["question"],
            "context": test_case["context"],
            "response": response.choices[0].message.content,
            "system_prompt": test_case["system_prompt"]
        })
    
    return traced_responses

def export_traces_for_evaluation(session):
    """
    Export traces from Phoenix for evaluation.
    """
    print("📤 Exporting traces for evaluation...")
    
    # Get the Phoenix client
    client = px.Client(endpoint=session.url)
    
    try:
        # Export spans as a dataframe
        spans_df = px.active_session().get_spans_dataframe()
        
        if spans_df.empty:
            print("⚠️  No spans found. Make sure LLM calls were traced.")
            return None
        
        # Filter for LLM spans and extract relevant data
        llm_spans = spans_df[spans_df['span_kind'] == 'LLM'].copy()
        
        if llm_spans.empty:
            print("⚠️  No LLM spans found.")
            return None
        
        # Extract input, output, and context for evaluation
        evaluation_data = []
        for _, span in llm_spans.iterrows():
            # Extract data from span attributes
            attributes = span.get('attributes', {})
            
            # Get input and output
            input_messages = attributes.get('llm.input_messages', [])
            output_message = attributes.get('llm.output_messages', [])
            
            if input_messages and output_message:
                # Extract user question and context
                user_content = ""
                context = ""
                
                for msg in input_messages:
                    if msg.get('message.role') == 'user':
                        content = msg.get('message.content', '')
                        if 'Context:' in content and 'Question:' in content:
                            parts = content.split('Question:')
                            if len(parts) == 2:
                                context = parts[0].replace('Context:', '').strip()
                                user_content = parts[1].strip()
                        else:
                            user_content = content
                
                # Extract response
                response_content = ""
                if output_message and len(output_message) > 0:
                    response_content = output_message[0].get('message.content', '')
                
                if user_content and response_content:
                    evaluation_data.append({
                        "input": user_content,
                        "output": response_content,
                        "context": context,
                        "span_id": span.name
                    })
        
        if not evaluation_data:
            print("⚠️  Could not extract evaluation data from spans.")
            return None
        
        eval_df = pd.DataFrame(evaluation_data)
        print(f"✅ Exported {len(eval_df)} spans for evaluation")
        return eval_df
        
    except Exception as e:
        print(f"❌ Error exporting traces: {str(e)}")
        return None

def run_evaluations_on_traces(traces_df):
    """
    Run evaluations on the exported traces.
    """
    if traces_df is None or traces_df.empty:
        print("❌ No traces available for evaluation")
        return None, None
    
    print("⚡ Running evaluations on traced data...")
    
    # Setup evaluation model
    eval_model = OpenAIModel(model="gpt-4o", temperature=0.0)
    
    # Setup evaluators
    hallucination_evaluator = HallucinationEvaluator(eval_model)
    qa_evaluator = QAEvaluator(eval_model)
    
    # Add reference column for QA evaluator (using context as reference)
    traces_df["reference"] = traces_df["context"]
    
    # Run evaluations
    print("   - Checking for hallucinations...")
    hallucination_results = run_evals(
        dataframe=traces_df,
        evaluators=[hallucination_evaluator],
        provide_explanation=True
    )[0]
    
    print("   - Evaluating Q&A correctness...")
    qa_results = run_evals(
        dataframe=traces_df,
        evaluators=[qa_evaluator],
        provide_explanation=True
    )[0]
    
    return hallucination_results, qa_results

def log_evaluations_to_phoenix(session, traces_df, hallucination_results, qa_results):
    """
    Log evaluation results back to Phoenix.
    """
    print("📊 Logging evaluation results to Phoenix...")
    
    try:
        # Create evaluation records
        evaluations = []
        
        for i, (_, row) in enumerate(traces_df.iterrows()):
            # Hallucination evaluation
            evaluations.append(SpanEvaluations(
                span_id=row["span_id"],
                name="hallucination",
                label=hallucination_results.iloc[i]["label"],
                score=1.0 if hallucination_results.iloc[i]["label"] == "factual" else 0.0,
                explanation=hallucination_results.iloc[i]["explanation"]
            ))
            
            # Q&A evaluation
            evaluations.append(SpanEvaluations(
                span_id=row["span_id"],
                name="qa_correctness",
                label=qa_results.iloc[i]["label"],
                score=1.0 if qa_results.iloc[i]["label"] == "correct" else 0.0,
                explanation=qa_results.iloc[i]["explanation"]
            ))
        
        # Log evaluations to Phoenix
        px.log_evaluations(evaluations)
        print("✅ Evaluation results logged to Phoenix")
        
    except Exception as e:
        print(f"⚠️  Could not log evaluations to Phoenix: {str(e)}")
        print("   Results are still available in the returned dataframes")

def analyze_tracing_results(traces_df, hallucination_results, qa_results):
    """
    Analyze and display the tracing + evaluation results.
    """
    print("\n📈 Tracing + Evaluation Results")
    print("=" * 40)
    
    if traces_df is None:
        print("❌ No results to analyze")
        return None
    
    # Combine results
    results_df = traces_df.copy()
    results_df["hallucination_score"] = hallucination_results["label"]
    results_df["hallucination_explanation"] = hallucination_results["explanation"]
    results_df["qa_score"] = qa_results["label"]
    results_df["qa_explanation"] = qa_results["explanation"]
    
    # Display summary
    print(f"📊 Summary:")
    print(f"   Total traced calls: {len(results_df)}")
    print(f"   Hallucinations detected: {sum(results_df['hallucination_score'] == 'hallucinated')}")
    print(f"   Correct Q&A responses: {sum(results_df['qa_score'] == 'correct')}")
    
    # Display detailed results
    print(f"\n📋 Detailed Results:")
    for idx, row in results_df.iterrows():
        print(f"\n--- Traced Call {idx + 1} ---")
        print(f"Question: {row['input']}")
        print(f"Response: {row['output'][:100]}{'...' if len(row['output']) > 100 else ''}")
        print(f"Hallucination: {row['hallucination_score']} - {row['hallucination_explanation']}")
        print(f"Q&A Correctness: {row['qa_score']} - {row['qa_explanation']}")
    
    return results_df

def main():
    """
    Main function to run the tracing + evaluation example.
    """
    try:
        # Setup tracing
        session = setup_tracing()
        
        # Make traced LLM calls
        traced_responses = create_traced_llm_calls()
        
        # Wait a moment for traces to be processed
        import time
        print("⏳ Waiting for traces to be processed...")
        time.sleep(3)
        
        # Export traces for evaluation
        traces_df = export_traces_for_evaluation(session)
        
        if traces_df is not None:
            # Run evaluations on traces
            hallucination_results, qa_results = run_evaluations_on_traces(traces_df)
            
            if hallucination_results is not None and qa_results is not None:
                # Log evaluations back to Phoenix
                log_evaluations_to_phoenix(session, traces_df, hallucination_results, qa_results)
                
                # Analyze results
                results_df = analyze_tracing_results(traces_df, hallucination_results, qa_results)
                
                # Save results
                if results_df is not None:
                    results_df.to_csv("tracing_evaluation_results.csv", index=False)
                    print(f"\n💾 Results saved to 'tracing_evaluation_results.csv'")
        
        print(f"\n✅ Tracing + evaluation example completed!")
        print(f"🔗 View traces and evaluations in Phoenix UI: {session.url}")
        
    except Exception as e:
        print(f"❌ Error running tracing + evaluation: {str(e)}")
        print("Make sure you have set your OPENAI_API_KEY environment variable.")

if __name__ == "__main__":
    main()
