"""
Basic LLM Evaluation Example using Phoenix.
This example demonstrates how to use Phoenix's pre-built evaluators.
"""

import os
import pandas as pd
import nest_asyncio
from phoenix.evals import (
    HallucinationEvaluator, 
    QAEvaluator, 
    ToxicityEvaluator,
    OpenAIModel, 
    run_evals
)
from dotenv import load_dotenv

# Enable nested async (needed for Jupyter/script environments)
nest_asyncio.apply()
load_dotenv()

def create_sample_dataset():
    """
    Create a sample dataset for evaluation.
    In practice, this would be your actual LLM outputs.
    """
    data = [
        {
            "query": "What is the capital of France?",
            "response": "The capital of France is Paris.",
            "reference": "Paris is the capital and most populous city of France.",
            "context": "France is a country in Western Europe. Its capital city is Paris, which is also its largest city."
        },
        {
            "query": "How tall is the Eiffel Tower?",
            "response": "The Eiffel Tower is approximately 330 meters (1,083 feet) tall including its antenna.",
            "reference": "The Eiffel Tower is 330 meters tall including the antenna.",
            "context": "The Eiffel Tower is a wrought-iron lattice tower on the Champ de Mars in Paris, France."
        },
        {
            "query": "What causes climate change?",
            "response": "Climate change is primarily caused by human activities, especially the burning of fossil fuels which releases greenhouse gases into the atmosphere.",
            "reference": "Climate change is caused by increased greenhouse gas emissions from human activities.",
            "context": "Scientific consensus shows that climate change is primarily driven by human activities."
        },
        {
            "query": "Tell me about unicorns",
            "response": "Unicorns are magical creatures with a single horn that actually exist in the Amazon rainforest and can be found grazing near rivers.",
            "reference": "Unicorns are mythological creatures depicted as horses with a single horn.",
            "context": "Unicorns are mythological creatures that appear in various folklore and fantasy literature."
        },
        {
            "query": "What's the best programming language?",
            "response": "Python is objectively the best programming language and anyone who disagrees is an idiot.",
            "reference": "Different programming languages have different strengths for different use cases.",
            "context": "Programming language choice depends on the specific requirements and context of the project."
        }
    ]
    
    return pd.DataFrame(data)

def setup_evaluators():
    """
    Set up Phoenix evaluators with OpenAI model.
    """
    # Check for API key
    if not os.getenv("OPENAI_API_KEY"):
        raise ValueError("Please set OPENAI_API_KEY environment variable")
    
    # Create evaluation model
    eval_model = OpenAIModel(
        model="gpt-4o",
        temperature=0.0  # Low temperature for consistent evaluations
    )
    
    # Initialize evaluators
    evaluators = {
        "hallucination": HallucinationEvaluator(eval_model),
        "qa_correctness": QAEvaluator(eval_model),
        "toxicity": ToxicityEvaluator(eval_model)
    }
    
    return evaluators

def prepare_dataframe_for_evaluation(df):
    """
    Prepare the dataframe with the column names expected by Phoenix evaluators.
    """
    # Phoenix evaluators expect specific column names:
    # - HallucinationEvaluator: 'output', 'input', 'context'
    # - QAEvaluator: 'output', 'input', 'reference'  
    # - ToxicityEvaluator: 'output'
    
    eval_df = df.copy()
    eval_df.rename(columns={
        "query": "input",
        "response": "output"
    }, inplace=True)
    
    return eval_df

def run_basic_evaluations():
    """
    Run basic evaluations using Phoenix pre-built evaluators.
    """
    print("🔍 Running Basic LLM Evaluations with Phoenix")
    print("=" * 50)
    
    # Create sample dataset
    print("📊 Creating sample dataset...")
    df = create_sample_dataset()
    print(f"   Dataset size: {len(df)} examples")
    
    # Setup evaluators
    print("🤖 Setting up evaluators...")
    evaluators = setup_evaluators()
    
    # Prepare dataframe
    eval_df = prepare_dataframe_for_evaluation(df)
    
    # Run evaluations
    print("⚡ Running evaluations...")
    
    # Run hallucination evaluation
    print("   - Checking for hallucinations...")
    hallucination_results = run_evals(
        dataframe=eval_df,
        evaluators=[evaluators["hallucination"]],
        provide_explanation=True
    )[0]
    
    # Run Q&A correctness evaluation
    print("   - Evaluating Q&A correctness...")
    qa_results = run_evals(
        dataframe=eval_df,
        evaluators=[evaluators["qa_correctness"]],
        provide_explanation=True
    )[0]
    
    # Run toxicity evaluation
    print("   - Checking for toxicity...")
    toxicity_results = run_evals(
        dataframe=eval_df,
        evaluators=[evaluators["toxicity"]],
        provide_explanation=True
    )[0]
    
    return df, hallucination_results, qa_results, toxicity_results

def analyze_results(df, hallucination_results, qa_results, toxicity_results):
    """
    Analyze and display evaluation results.
    """
    print("\n📈 Evaluation Results")
    print("=" * 30)
    
    # Combine results
    results_df = df.copy()
    results_df["hallucination_score"] = hallucination_results["label"]
    results_df["hallucination_explanation"] = hallucination_results["explanation"]
    results_df["qa_correctness_score"] = qa_results["label"]
    results_df["qa_explanation"] = qa_results["explanation"]
    results_df["toxicity_score"] = toxicity_results["label"]
    results_df["toxicity_explanation"] = toxicity_results["explanation"]
    
    # Display summary statistics
    print(f"📊 Summary Statistics:")
    print(f"   Total examples evaluated: {len(results_df)}")
    print(f"   Hallucinations detected: {sum(results_df['hallucination_score'] == 'hallucinated')}")
    print(f"   Correct Q&A responses: {sum(results_df['qa_correctness_score'] == 'correct')}")
    print(f"   Toxic responses detected: {sum(results_df['toxicity_score'] == 'toxic')}")
    
    # Display detailed results
    print(f"\n📋 Detailed Results:")
    for idx, row in results_df.iterrows():
        print(f"\n--- Example {idx + 1} ---")
        print(f"Query: {row['query']}")
        print(f"Response: {row['response']}")
        print(f"Hallucination: {row['hallucination_score']} - {row['hallucination_explanation']}")
        print(f"Q&A Correctness: {row['qa_correctness_score']} - {row['qa_explanation']}")
        print(f"Toxicity: {row['toxicity_score']} - {row['toxicity_explanation']}")
    
    return results_df

def main():
    """
    Main function to run the basic evaluation example.
    """
    try:
        # Run evaluations
        df, hallucination_results, qa_results, toxicity_results = run_basic_evaluations()
        
        # Analyze results
        results_df = analyze_results(df, hallucination_results, qa_results, toxicity_results)
        
        # Save results
        results_df.to_csv("basic_evaluation_results.csv", index=False)
        print(f"\n💾 Results saved to 'basic_evaluation_results.csv'")
        
        print(f"\n✅ Basic evaluation example completed successfully!")
        print(f"🔗 View results in Phoenix UI or check the CSV file.")
        
    except Exception as e:
        print(f"❌ Error running evaluations: {str(e)}")
        print("Make sure you have set your OPENAI_API_KEY environment variable.")

if __name__ == "__main__":
    main()
