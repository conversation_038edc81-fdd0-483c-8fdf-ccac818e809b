"""
Custom Evaluator Example using Phoenix.
This example shows how to create and use custom evaluators for specific use cases.
"""

import os
import pandas as pd
import nest_asyncio
from phoenix.evals import OpenAIModel, run_evals
from phoenix.evals.evaluators import BaseEvaluator
from phoenix.evals.templates import ClassificationTemplate
from dotenv import load_dotenv

# Enable nested async
nest_asyncio.apply()
load_dotenv()

class CodeQualityEvaluator(BaseEvaluator):
    """
    Custom evaluator to assess code quality in LLM responses.
    """
    
    def __init__(self, model):
        # Define the evaluation criteria and template
        template = ClassificationTemplate(
            criteria="""
            You are evaluating the quality of code provided in a response. 
            Consider the following aspects:
            1. Correctness: Does the code solve the problem correctly?
            2. Readability: Is the code well-structured and easy to understand?
            3. Best Practices: Does the code follow programming best practices?
            4. Completeness: Is the solution complete and functional?
            
            Rate the code quality as:
            - excellent: High quality code that meets all criteria
            - good: Decent code with minor issues
            - poor: Code with significant problems or errors
            """,
            labels=["excellent", "good", "poor"],
            explanation_template="Explain your reasoning for the code quality rating, "
                               "considering correctness, readability, best practices, and completeness."
        )
        super().__init__(model=model, template=template)

class SentimentEvaluator(BaseEvaluator):
    """
    Custom evaluator to assess sentiment in LLM responses.
    """
    
    def __init__(self, model):
        template = ClassificationTemplate(
            criteria="""
            Analyze the sentiment of the given response. Consider the overall tone,
            emotional content, and attitude expressed in the text.
            
            Classify the sentiment as:
            - positive: Optimistic, happy, or encouraging tone
            - neutral: Balanced, factual, or objective tone  
            - negative: Pessimistic, sad, or discouraging tone
            """,
            labels=["positive", "neutral", "negative"],
            explanation_template="Explain why you classified this response with this sentiment, "
                               "citing specific words or phrases that influenced your decision."
        )
        super().__init__(model=model, template=template)

class FactualAccuracyEvaluator(BaseEvaluator):
    """
    Custom evaluator to check factual accuracy against provided context.
    """
    
    def __init__(self, model):
        template = ClassificationTemplate(
            criteria="""
            Evaluate whether the response is factually accurate based on the provided context.
            Check if the information in the response aligns with the facts in the context.
            
            Rate factual accuracy as:
            - accurate: All facts in the response are correct according to the context
            - partially_accurate: Some facts are correct but there are minor inaccuracies
            - inaccurate: Contains significant factual errors or contradicts the context
            """,
            labels=["accurate", "partially_accurate", "inaccurate"],
            explanation_template="Explain your factual accuracy assessment, identifying any "
                               "specific facts that are correct or incorrect compared to the context."
        )
        super().__init__(model=model, template=template)

def create_diverse_dataset():
    """
    Create a diverse dataset for testing custom evaluators.
    """
    data = [
        {
            "query": "Write a Python function to calculate factorial",
            "response": """
def factorial(n):
    if n < 0:
        return None
    elif n == 0 or n == 1:
        return 1
    else:
        return n * factorial(n - 1)
""",
            "context": "A factorial function should handle edge cases and use recursion or iteration.",
            "category": "code"
        },
        {
            "query": "How do you feel about the new AI developments?",
            "response": "I'm incredibly excited about the recent breakthroughs in AI! The possibilities are endless and I believe we're on the verge of revolutionary changes that will benefit humanity.",
            "context": "AI developments have both positive and negative implications.",
            "category": "sentiment"
        },
        {
            "query": "What is the speed of light?",
            "response": "The speed of light in a vacuum is approximately 299,792,458 meters per second, which is often rounded to 300,000 kilometers per second for practical calculations.",
            "context": "The speed of light in vacuum is exactly 299,792,458 meters per second.",
            "category": "factual"
        },
        {
            "query": "Write a function to reverse a string",
            "response": """
def reverse_string(s):
    return s[::-1]
""",
            "context": "String reversal can be done using slicing, loops, or built-in functions.",
            "category": "code"
        },
        {
            "query": "What do you think about rainy weather?",
            "response": "Rainy weather can be quite depressing and gloomy. It makes me feel sad and unmotivated to do anything productive.",
            "context": "Weather can affect mood in different ways for different people.",
            "category": "sentiment"
        },
        {
            "query": "When was the first moon landing?",
            "response": "The first moon landing occurred on July 20, 1969, when Apollo 11 astronauts Neil Armstrong and Buzz Aldrin landed on the lunar surface.",
            "context": "Apollo 11 landed on the Moon on July 20, 1969. Neil Armstrong was the first person to walk on the Moon.",
            "category": "factual"
        }
    ]
    
    return pd.DataFrame(data)

def setup_custom_evaluators():
    """
    Set up custom evaluators.
    """
    if not os.getenv("OPENAI_API_KEY"):
        raise ValueError("Please set OPENAI_API_KEY environment variable")
    
    eval_model = OpenAIModel(model="gpt-4o", temperature=0.0)
    
    evaluators = {
        "code_quality": CodeQualityEvaluator(eval_model),
        "sentiment": SentimentEvaluator(eval_model),
        "factual_accuracy": FactualAccuracyEvaluator(eval_model)
    }
    
    return evaluators

def prepare_dataframe_for_custom_evaluation(df):
    """
    Prepare dataframe for custom evaluators.
    """
    eval_df = df.copy()
    eval_df.rename(columns={
        "query": "input",
        "response": "output"
    }, inplace=True)
    
    return eval_df

def run_custom_evaluations():
    """
    Run evaluations using custom evaluators.
    """
    print("🔧 Running Custom LLM Evaluations with Phoenix")
    print("=" * 50)
    
    # Create dataset
    print("📊 Creating diverse dataset...")
    df = create_diverse_dataset()
    print(f"   Dataset size: {len(df)} examples")
    
    # Setup evaluators
    print("🤖 Setting up custom evaluators...")
    evaluators = setup_custom_evaluators()
    
    # Prepare dataframe
    eval_df = prepare_dataframe_for_custom_evaluation(df)
    
    # Run evaluations by category
    results = {}
    
    # Code quality evaluation (only for code examples)
    print("⚡ Running code quality evaluation...")
    code_examples = eval_df[df["category"] == "code"]
    if not code_examples.empty:
        results["code_quality"] = run_evals(
            dataframe=code_examples,
            evaluators=[evaluators["code_quality"]],
            provide_explanation=True
        )[0]
    
    # Sentiment evaluation (only for sentiment examples)
    print("⚡ Running sentiment evaluation...")
    sentiment_examples = eval_df[df["category"] == "sentiment"]
    if not sentiment_examples.empty:
        results["sentiment"] = run_evals(
            dataframe=sentiment_examples,
            evaluators=[evaluators["sentiment"]],
            provide_explanation=True
        )[0]
    
    # Factual accuracy evaluation (only for factual examples)
    print("⚡ Running factual accuracy evaluation...")
    factual_examples = eval_df[df["category"] == "factual"]
    if not factual_examples.empty:
        results["factual_accuracy"] = run_evals(
            dataframe=factual_examples,
            evaluators=[evaluators["factual_accuracy"]],
            provide_explanation=True
        )[0]
    
    return df, results

def analyze_custom_results(df, results):
    """
    Analyze and display custom evaluation results.
    """
    print("\n📈 Custom Evaluation Results")
    print("=" * 35)
    
    # Create results dataframe
    results_df = df.copy()
    
    # Add evaluation results based on category
    results_df["evaluation_score"] = ""
    results_df["evaluation_explanation"] = ""
    
    for category, eval_results in results.items():
        category_mask = df["category"] == category.replace("_", "")
        if category == "code_quality":
            category_mask = df["category"] == "code"
        elif category == "factual_accuracy":
            category_mask = df["category"] == "factual"
        
        results_df.loc[category_mask, "evaluation_score"] = eval_results["label"].values
        results_df.loc[category_mask, "evaluation_explanation"] = eval_results["explanation"].values
    
    # Display summary
    print("📊 Summary by Category:")
    for category in df["category"].unique():
        category_data = results_df[results_df["category"] == category]
        print(f"\n{category.upper()} Examples:")
        print(f"   Count: {len(category_data)}")
        if not category_data["evaluation_score"].empty:
            score_counts = category_data["evaluation_score"].value_counts()
            for score, count in score_counts.items():
                print(f"   {score}: {count}")
    
    # Display detailed results
    print(f"\n📋 Detailed Results:")
    for idx, row in results_df.iterrows():
        print(f"\n--- Example {idx + 1} ({row['category'].upper()}) ---")
        print(f"Query: {row['query']}")
        print(f"Response: {row['response'][:100]}{'...' if len(row['response']) > 100 else ''}")
        print(f"Evaluation: {row['evaluation_score']}")
        print(f"Explanation: {row['evaluation_explanation']}")
    
    return results_df

def main():
    """
    Main function to run the custom evaluation example.
    """
    try:
        # Run custom evaluations
        df, results = run_custom_evaluations()
        
        # Analyze results
        results_df = analyze_custom_results(df, results)
        
        # Save results
        results_df.to_csv("custom_evaluation_results.csv", index=False)
        print(f"\n💾 Results saved to 'custom_evaluation_results.csv'")
        
        print(f"\n✅ Custom evaluation example completed successfully!")
        print(f"🔗 View results in Phoenix UI or check the CSV file.")
        
    except Exception as e:
        print(f"❌ Error running custom evaluations: {str(e)}")
        print("Make sure you have set your OPENAI_API_KEY environment variable.")

if __name__ == "__main__":
    main()
