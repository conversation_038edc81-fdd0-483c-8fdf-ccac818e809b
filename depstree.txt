anthropic==0.52.2
├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│   ├── idna [required: >=2.8, installed: 3.10]
│   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   └── typing_extensions [required: >=4.5, installed: 4.14.0]
├── distro [required: >=1.7.0,<2, installed: 1.9.0]
├── httpx [required: >=0.25.0,<1, installed: 0.28.1]
│   ├── anyio [required: Any, installed: 4.9.0]
│   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   ├── certifi [required: Any, installed: 2025.4.26]
│   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   └── idna [required: Any, installed: 3.10]
├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
├── sniffio [required: Any, installed: 1.3.1]
└── typing_extensions [required: >=4.10,<5, installed: 4.14.0]
arize-phoenix==10.6.2
├── aioitertools [required: Any, installed: 0.12.0]
├── aiosqlite [required: Any, installed: 0.21.0]
│   └── typing_extensions [required: >=4.0, installed: 4.14.0]
├── alembic [required: >=1.3.0,<2, installed: 1.16.1]
│   ├── SQLAlchemy [required: >=1.4.0, installed: 2.0.41]
│   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   ├── Mako [required: Any, installed: 1.3.10]
│   │   └── MarkupSafe [required: >=0.9.2, installed: 3.0.2]
│   └── typing_extensions [required: >=4.12, installed: 4.14.0]
├── arize-phoenix-client [required: Any, installed: 1.9.0]
│   ├── httpx [required: Any, installed: 0.28.1]
│   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   └── idna [required: Any, installed: 3.10]
│   └── typing_extensions [required: Any, installed: 4.14.0]
├── arize-phoenix-evals [required: >=0.20.6, installed: 0.20.7]
│   ├── pandas [required: Any, installed: 2.2.3]
│   │   ├── numpy [required: >=1.26.0, installed: 2.2.6]
│   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   ├── pytz [required: >=2020.1, installed: 2025.2]
│   │   └── tzdata [required: >=2022.7, installed: 2025.2]
│   ├── tqdm [required: Any, installed: 4.67.1]
│   └── typing_extensions [required: >=4.5,<5, installed: 4.14.0]
├── arize-phoenix-otel [required: >=0.10.1, installed: 0.10.2]
│   ├── openinference-instrumentation [required: >=0.1.32, installed: 0.1.32]
│   │   ├── openinference-semantic-conventions [required: >=0.1.17, installed: 0.1.17]
│   │   ├── opentelemetry-api [required: Any, installed: 1.33.1]
│   │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │   │       └── zipp [required: >=3.20, installed: 3.22.0]
│   │   └── opentelemetry-sdk [required: Any, installed: 1.33.1]
│   │       ├── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │       │       └── zipp [required: >=3.20, installed: 3.22.0]
│   │       ├── opentelemetry-semantic-conventions [required: ==0.54b1, installed: 0.54b1]
│   │       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │       │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │       │           └── zipp [required: >=3.20, installed: 3.22.0]
│   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   ├── openinference-semantic-conventions [required: >=0.1.17, installed: 0.1.17]
│   ├── opentelemetry-exporter-otlp [required: Any, installed: 1.33.1]
│   │   ├── opentelemetry-exporter-otlp-proto-grpc [required: ==1.33.1, installed: 1.33.1]
│   │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── googleapis-common-protos [required: ~=1.52, installed: 1.70.0]
│   │   │   │   └── protobuf [required: >=3.20.2,<7.0.0,!=4.21.5,!=4.21.4,!=4.21.3,!=4.21.2,!=4.21.1, installed: 5.29.5]
│   │   │   ├── grpcio [required: >=1.63.2,<2.0.0, installed: 1.72.1]
│   │   │   ├── opentelemetry-api [required: ~=1.15, installed: 1.33.1]
│   │   │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │   │   │       └── zipp [required: >=3.20, installed: 3.22.0]
│   │   │   ├── opentelemetry-exporter-otlp-proto-common [required: ==1.33.1, installed: 1.33.1]
│   │   │   │   └── opentelemetry-proto [required: ==1.33.1, installed: 1.33.1]
│   │   │   │       └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
│   │   │   ├── opentelemetry-proto [required: ==1.33.1, installed: 1.33.1]
│   │   │   │   └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
│   │   │   └── opentelemetry-sdk [required: ~=1.33.1, installed: 1.33.1]
│   │   │       ├── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │   │       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │       │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │   │       │       └── zipp [required: >=3.20, installed: 3.22.0]
│   │   │       ├── opentelemetry-semantic-conventions [required: ==0.54b1, installed: 0.54b1]
│   │   │       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │       │   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │   │       │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │       │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │       │       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │   │       │           └── zipp [required: >=3.20, installed: 3.22.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── opentelemetry-exporter-otlp-proto-http [required: ==1.33.1, installed: 1.33.1]
│   │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       ├── googleapis-common-protos [required: ~=1.52, installed: 1.70.0]
│   │       │   └── protobuf [required: >=3.20.2,<7.0.0,!=4.21.5,!=4.21.4,!=4.21.3,!=4.21.2,!=4.21.1, installed: 5.29.5]
│   │       ├── opentelemetry-api [required: ~=1.15, installed: 1.33.1]
│   │       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │       │       └── zipp [required: >=3.20, installed: 3.22.0]
│   │       ├── opentelemetry-exporter-otlp-proto-common [required: ==1.33.1, installed: 1.33.1]
│   │       │   └── opentelemetry-proto [required: ==1.33.1, installed: 1.33.1]
│   │       │       └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
│   │       ├── opentelemetry-proto [required: ==1.33.1, installed: 1.33.1]
│   │       │   └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
│   │       ├── opentelemetry-sdk [required: ~=1.33.1, installed: 1.33.1]
│   │       │   ├── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │       │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │   │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │       │   │       └── zipp [required: >=3.20, installed: 3.22.0]
│   │       │   ├── opentelemetry-semantic-conventions [required: ==0.54b1, installed: 0.54b1]
│   │       │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │   │   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │       │   │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │   │       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │       │   │           └── zipp [required: >=3.20, installed: 3.22.0]
│   │       │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │       └── requests [required: ~=2.7, installed: 2.32.3]
│   │           ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │           ├── idna [required: >=2.5,<4, installed: 3.10]
│   │           ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │           └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   ├── opentelemetry-proto [required: >=1.12.0, installed: 1.33.1]
│   │   └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
│   ├── opentelemetry-sdk [required: Any, installed: 1.33.1]
│   │   ├── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │   │       └── zipp [required: >=3.20, installed: 3.22.0]
│   │   ├── opentelemetry-semantic-conventions [required: ==0.54b1, installed: 0.54b1]
│   │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │   │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │   │           └── zipp [required: >=3.20, installed: 3.22.0]
│   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   ├── opentelemetry-semantic-conventions [required: Any, installed: 0.54b1]
│   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │           └── zipp [required: >=3.20, installed: 3.22.0]
│   └── typing_extensions [required: >=4.5,<5, installed: 4.14.0]
├── Authlib [required: Any, installed: 1.6.0]
│   └── cryptography [required: Any, installed: 45.0.3]
│       └── cffi [required: >=1.14, installed: 1.17.1]
│           └── pycparser [required: Any, installed: 2.22]
├── cachetools [required: Any, installed: 6.0.0]
├── email_validator [required: Any, installed: 2.2.0]
│   ├── dnspython [required: >=2.0.0, installed: 2.7.0]
│   └── idna [required: >=2.0.0, installed: 3.10]
├── fastapi [required: Any, installed: 0.115.12]
│   ├── starlette [required: >=0.40.0,<0.47.0, installed: 0.46.2]
│   │   └── anyio [required: >=3.6.2,<5, installed: 4.9.0]
│   │       ├── idna [required: >=2.8, installed: 3.10]
│   │       ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   ├── pydantic [required: >=1.7.4,<3.0.0,!=2.1.0,!=2.0.1,!=2.0.0,!=1.8.1,!=1.8, installed: 2.11.5]
│   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   └── typing_extensions [required: >=4.8.0, installed: 4.14.0]
├── grpc-interceptor [required: Any, installed: 0.15.4]
│   └── grpcio [required: >=1.49.1,<2.0.0, installed: 1.72.1]
├── grpcio [required: Any, installed: 1.72.1]
├── httpx [required: Any, installed: 0.28.1]
│   ├── anyio [required: Any, installed: 4.9.0]
│   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   ├── certifi [required: Any, installed: 2025.4.26]
│   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   └── idna [required: Any, installed: 3.10]
├── Jinja2 [required: Any, installed: 3.1.6]
│   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
├── numpy [required: !=2.0.0, installed: 2.2.6]
├── openinference-instrumentation [required: >=0.1.32, installed: 0.1.32]
│   ├── openinference-semantic-conventions [required: >=0.1.17, installed: 0.1.17]
│   ├── opentelemetry-api [required: Any, installed: 1.33.1]
│   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │       └── zipp [required: >=3.20, installed: 3.22.0]
│   └── opentelemetry-sdk [required: Any, installed: 1.33.1]
│       ├── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│       │       └── zipp [required: >=3.20, installed: 3.22.0]
│       ├── opentelemetry-semantic-conventions [required: ==0.54b1, installed: 0.54b1]
│       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│       │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│       │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│       │           └── zipp [required: >=3.20, installed: 3.22.0]
│       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
├── openinference-semantic-conventions [required: >=0.1.17, installed: 0.1.17]
├── opentelemetry-exporter-otlp [required: Any, installed: 1.33.1]
│   ├── opentelemetry-exporter-otlp-proto-grpc [required: ==1.33.1, installed: 1.33.1]
│   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── googleapis-common-protos [required: ~=1.52, installed: 1.70.0]
│   │   │   └── protobuf [required: >=3.20.2,<7.0.0,!=4.21.5,!=4.21.4,!=4.21.3,!=4.21.2,!=4.21.1, installed: 5.29.5]
│   │   ├── grpcio [required: >=1.63.2,<2.0.0, installed: 1.72.1]
│   │   ├── opentelemetry-api [required: ~=1.15, installed: 1.33.1]
│   │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │   │       └── zipp [required: >=3.20, installed: 3.22.0]
│   │   ├── opentelemetry-exporter-otlp-proto-common [required: ==1.33.1, installed: 1.33.1]
│   │   │   └── opentelemetry-proto [required: ==1.33.1, installed: 1.33.1]
│   │   │       └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
│   │   ├── opentelemetry-proto [required: ==1.33.1, installed: 1.33.1]
│   │   │   └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
│   │   └── opentelemetry-sdk [required: ~=1.33.1, installed: 1.33.1]
│   │       ├── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │       │       └── zipp [required: >=3.20, installed: 3.22.0]
│   │       ├── opentelemetry-semantic-conventions [required: ==0.54b1, installed: 0.54b1]
│   │       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │       │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       │       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │       │           └── zipp [required: >=3.20, installed: 3.22.0]
│   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   └── opentelemetry-exporter-otlp-proto-http [required: ==1.33.1, installed: 1.33.1]
│       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       ├── googleapis-common-protos [required: ~=1.52, installed: 1.70.0]
│       │   └── protobuf [required: >=3.20.2,<7.0.0,!=4.21.5,!=4.21.4,!=4.21.3,!=4.21.2,!=4.21.1, installed: 5.29.5]
│       ├── opentelemetry-api [required: ~=1.15, installed: 1.33.1]
│       │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│       │       └── zipp [required: >=3.20, installed: 3.22.0]
│       ├── opentelemetry-exporter-otlp-proto-common [required: ==1.33.1, installed: 1.33.1]
│       │   └── opentelemetry-proto [required: ==1.33.1, installed: 1.33.1]
│       │       └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
│       ├── opentelemetry-proto [required: ==1.33.1, installed: 1.33.1]
│       │   └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
│       ├── opentelemetry-sdk [required: ~=1.33.1, installed: 1.33.1]
│       │   ├── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│       │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│       │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│       │   │       └── zipp [required: >=3.20, installed: 3.22.0]
│       │   ├── opentelemetry-semantic-conventions [required: ==0.54b1, installed: 0.54b1]
│       │   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│       │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│       │   │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│       │   │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│       │   │           └── zipp [required: >=3.20, installed: 3.22.0]
│       │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       └── requests [required: ~=2.7, installed: 2.32.3]
│           ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│           ├── idna [required: >=2.5,<4, installed: 3.10]
│           ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│           └── certifi [required: >=2017.4.17, installed: 2025.4.26]
├── opentelemetry-proto [required: >=1.12.0, installed: 1.33.1]
│   └── protobuf [required: >=5.0,<6.0, installed: 5.29.5]
├── opentelemetry-sdk [required: Any, installed: 1.33.1]
│   ├── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │       └── zipp [required: >=3.20, installed: 3.22.0]
│   ├── opentelemetry-semantic-conventions [required: ==0.54b1, installed: 0.54b1]
│   │   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│   │       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│   │           └── zipp [required: >=3.20, installed: 3.22.0]
│   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
├── opentelemetry-semantic-conventions [required: Any, installed: 0.54b1]
│   ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   └── opentelemetry-api [required: ==1.33.1, installed: 1.33.1]
│       ├── Deprecated [required: >=1.2.6, installed: 1.2.18]
│       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       └── importlib_metadata [required: >=6.0,<8.7.0, installed: 8.6.1]
│           └── zipp [required: >=3.20, installed: 3.22.0]
├── pandas [required: >=1.0, installed: 2.2.3]
│   ├── numpy [required: >=1.26.0, installed: 2.2.6]
│   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   └── six [required: >=1.5, installed: 1.17.0]
│   ├── pytz [required: >=2020.1, installed: 2025.2]
│   └── tzdata [required: >=2022.7, installed: 2025.2]
├── protobuf [required: >=3.20.2,<6.0, installed: 5.29.5]
├── psutil [required: Any, installed: 7.0.0]
├── pyarrow [required: Any, installed: 20.0.0]
├── pydantic [required: >=2.1.0, installed: 2.11.5]
│   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
├── python-multipart [required: Any, installed: 0.0.20]
├── scikit-learn [required: Any, installed: 1.6.1]
│   ├── numpy [required: >=1.19.5, installed: 2.2.6]
│   ├── scipy [required: >=1.6.0, installed: 1.15.3]
│   │   └── numpy [required: >=1.23.5,<2.5, installed: 2.2.6]
│   ├── joblib [required: >=1.2.0, installed: 1.5.1]
│   └── threadpoolctl [required: >=3.1.0, installed: 3.6.0]
├── scipy [required: Any, installed: 1.15.3]
│   └── numpy [required: >=1.23.5,<2.5, installed: 2.2.6]
├── SQLAlchemy [required: >=2.0.4,<3, installed: 2.0.41]
│   ├── greenlet [required: >=1, installed: 3.2.2]
│   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
├── sqlean.py [required: >=3.45.1, installed: 3.49.1]
├── starlette [required: Any, installed: 0.46.2]
│   └── anyio [required: >=3.6.2,<5, installed: 4.9.0]
│       ├── idna [required: >=2.8, installed: 3.10]
│       ├── sniffio [required: >=1.1, installed: 1.3.1]
│       └── typing_extensions [required: >=4.5, installed: 4.14.0]
├── strawberry-graphql [required: ==0.270.1, installed: 0.270.1]
│   ├── graphql-core [required: >=3.2.0,<3.4.0, installed: 3.2.6]
│   ├── packaging [required: >=23, installed: 24.2]
│   ├── python-dateutil [required: >=2.7.0,<3.0.0, installed: 2.9.0.post0]
│   │   └── six [required: >=1.5, installed: 1.17.0]
│   └── typing_extensions [required: >=4.5.0, installed: 4.14.0]
├── tqdm [required: Any, installed: 4.67.1]
├── typing_extensions [required: >=4.6, installed: 4.14.0]
├── uvicorn [required: Any, installed: 0.34.3]
│   ├── click [required: >=7.0, installed: 8.2.1]
│   └── h11 [required: >=0.8, installed: 0.16.0]
└── wrapt [required: >=1.17.2, installed: 1.17.2]
asyncio==3.4.3
fqdn==1.5.1
GitPython==3.1.41
└── gitdb [required: >=4.0.1,<5, installed: 4.0.12]
    └── smmap [required: >=3.0.1,<6, installed: 5.0.2]
isoduration==20.11.0
└── arrow [required: >=0.15.0, installed: 1.3.0]
    ├── python-dateutil [required: >=2.7.0, installed: 2.9.0.post0]
    │   └── six [required: >=1.5, installed: 1.17.0]
    └── types-python-dateutil [required: >=2.8.10, installed: 2.9.0.20250516]
jupyter==1.1.1
├── notebook [required: Any, installed: 7.4.3]
│   ├── jupyter_server [required: >=2.4.0,<3, installed: 2.16.0]
│   │   ├── anyio [required: >=3.1.0, installed: 4.9.0]
│   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
│   │   │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
│   │   │       └── cffi [required: >=1.0.1, installed: 1.17.1]
│   │   │           └── pycparser [required: Any, installed: 2.22]
│   │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
│   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
│   │   │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
│   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   ├── packaging [required: Any, installed: 24.2]
│   │   │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
│   │   │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
│   │   │   ├── referencing [required: Any, installed: 0.36.2]
│   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
│   │   │   │   └── six [required: Any, installed: 1.17.0]
│   │   │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
│   │   │   └── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │       ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │       └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
│   │   │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
│   │   │   │   ├── soupsieve [required: >1.2, installed: 2.7]
│   │   │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
│   │   │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
│   │   │   │   └── webencodings [required: Any, installed: 0.5.1]
│   │   │   ├── defusedxml [required: Any, installed: 0.7.1]
│   │   │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
│   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
│   │   │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
│   │   │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
│   │   │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
│   │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
│   │   │   ├── nbformat [required: >=5.7, installed: 5.10.4]
│   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   ├── packaging [required: Any, installed: 24.2]
│   │   │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
│   │   │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
│   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   ├── nbformat [required: >=5.3.0, installed: 5.10.4]
│   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   ├── overrides [required: >=5.0, installed: 7.7.0]
│   │   ├── packaging [required: >=22.0, installed: 24.2]
│   │   ├── prometheus_client [required: >=0.9, installed: 0.22.1]
│   │   ├── pyzmq [required: >=24, installed: 26.4.0]
│   │   ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
│   │   ├── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │   ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │   └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   ├── tornado [required: >=6.2.0, installed: 6.5.1]
│   │   ├── traitlets [required: >=5.6.0, installed: 5.14.3]
│   │   └── websocket-client [required: >=1.7, installed: 1.8.0]
│   ├── jupyterlab_server [required: >=2.27.1,<3, installed: 2.27.3]
│   │   ├── babel [required: >=2.10, installed: 2.17.0]
│   │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   ├── json5 [required: >=0.9.0, installed: 0.12.0]
│   │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
│   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   ├── jupyter_server [required: >=1.21,<3, installed: 2.16.0]
│   │   │   ├── anyio [required: >=3.1.0, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
│   │   │   │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
│   │   │   │       └── cffi [required: >=1.0.1, installed: 1.17.1]
│   │   │   │           └── pycparser [required: Any, installed: 2.22]
│   │   │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
│   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
│   │   │   │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   ├── packaging [required: Any, installed: 24.2]
│   │   │   │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
│   │   │   │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
│   │   │   │   ├── referencing [required: Any, installed: 0.36.2]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
│   │   │   │   │   └── six [required: Any, installed: 1.17.0]
│   │   │   │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
│   │   │   │   └── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │   │       ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │   │       └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │   ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
│   │   │   │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
│   │   │   │   │   ├── soupsieve [required: >1.2, installed: 2.7]
│   │   │   │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
│   │   │   │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
│   │   │   │   │   └── webencodings [required: Any, installed: 0.5.1]
│   │   │   │   ├── defusedxml [required: Any, installed: 0.7.1]
│   │   │   │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
│   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
│   │   │   │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
│   │   │   │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
│   │   │   │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
│   │   │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
│   │   │   │   ├── nbformat [required: >=5.7, installed: 5.10.4]
│   │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   │   ├── packaging [required: Any, installed: 24.2]
│   │   │   │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
│   │   │   │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
│   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   ├── nbformat [required: >=5.3.0, installed: 5.10.4]
│   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   ├── overrides [required: >=5.0, installed: 7.7.0]
│   │   │   ├── packaging [required: >=22.0, installed: 24.2]
│   │   │   ├── prometheus_client [required: >=0.9, installed: 0.22.1]
│   │   │   ├── pyzmq [required: >=24, installed: 26.4.0]
│   │   │   ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
│   │   │   ├── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │   │   ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │   │   └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │   ├── tornado [required: >=6.2.0, installed: 6.5.1]
│   │   │   ├── traitlets [required: >=5.6.0, installed: 5.14.3]
│   │   │   └── websocket-client [required: >=1.7, installed: 1.8.0]
│   │   ├── packaging [required: >=21.3, installed: 24.2]
│   │   └── requests [required: >=2.31, installed: 2.32.3]
│   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   ├── jupyterlab [required: >=4.4.3,<4.5, installed: 4.4.3]
│   │   ├── async-lru [required: >=1.0.0, installed: 2.0.5]
│   │   ├── httpx [required: >=0.25.0, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── ipykernel [required: >=6.5.0, installed: 6.29.5]
│   │   │   ├── comm [required: >=0.1.1, installed: 0.2.2]
│   │   │   │   └── traitlets [required: >=4, installed: 5.14.3]
│   │   │   ├── debugpy [required: >=1.6.5, installed: 1.8.14]
│   │   │   ├── ipython [required: >=7.23.1, installed: 9.3.0]
│   │   │   │   ├── decorator [required: Any, installed: 5.2.1]
│   │   │   │   ├── ipython_pygments_lexers [required: Any, installed: 1.1.1]
│   │   │   │   │   └── Pygments [required: Any, installed: 2.19.1]
│   │   │   │   ├── jedi [required: >=0.16, installed: 0.19.2]
│   │   │   │   │   └── parso [required: >=0.8.4,<0.9.0, installed: 0.8.4]
│   │   │   │   ├── matplotlib-inline [required: Any, installed: 0.1.7]
│   │   │   │   │   └── traitlets [required: Any, installed: 5.14.3]
│   │   │   │   ├── pexpect [required: >4.3, installed: 4.9.0]
│   │   │   │   │   └── ptyprocess [required: >=0.5, installed: 0.7.0]
│   │   │   │   ├── prompt_toolkit [required: >=3.0.41,<3.1.0, installed: 3.0.51]
│   │   │   │   │   └── wcwidth [required: Any, installed: 0.2.13]
│   │   │   │   ├── Pygments [required: >=2.4.0, installed: 2.19.1]
│   │   │   │   ├── stack-data [required: Any, installed: 0.6.3]
│   │   │   │   │   ├── executing [required: >=1.2.0, installed: 2.2.0]
│   │   │   │   │   ├── asttokens [required: >=2.1.0, installed: 3.0.0]
│   │   │   │   │   └── pure_eval [required: Any, installed: 0.2.3]
│   │   │   │   └── traitlets [required: >=5.13.0, installed: 5.14.3]
│   │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── matplotlib-inline [required: >=0.1, installed: 0.1.7]
│   │   │   │   └── traitlets [required: Any, installed: 5.14.3]
│   │   │   ├── nest-asyncio [required: Any, installed: 1.6.0]
│   │   │   ├── packaging [required: Any, installed: 24.2]
│   │   │   ├── psutil [required: Any, installed: 7.0.0]
│   │   │   ├── pyzmq [required: >=24, installed: 26.4.0]
│   │   │   ├── tornado [required: >=6.1, installed: 6.5.1]
│   │   │   └── traitlets [required: >=5.4.0, installed: 5.14.3]
│   │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   ├── jupyter_core [required: Any, installed: 5.8.1]
│   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── jupyter-lsp [required: >=2.0.0, installed: 2.2.5]
│   │   │   └── jupyter_server [required: >=1.1.2, installed: 2.16.0]
│   │   │       ├── anyio [required: >=3.1.0, installed: 4.9.0]
│   │   │       │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │       ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
│   │   │       │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
│   │   │       │       └── cffi [required: >=1.0.1, installed: 1.17.1]
│   │   │       │           └── pycparser [required: Any, installed: 2.22]
│   │   │       ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │   │       │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │       ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
│   │   │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │       │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │       │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │       │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
│   │   │       │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
│   │   │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │       │   ├── packaging [required: Any, installed: 24.2]
│   │   │       │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
│   │   │       │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
│   │   │       │   ├── referencing [required: Any, installed: 0.36.2]
│   │   │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
│   │   │       │   │   └── six [required: Any, installed: 1.17.0]
│   │   │       │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
│   │   │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
│   │   │       │   └── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │       │       ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │       │       └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │       ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
│   │   │       │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
│   │   │       │   │   ├── soupsieve [required: >1.2, installed: 2.7]
│   │   │       │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
│   │   │       │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
│   │   │       │   │   └── webencodings [required: Any, installed: 0.5.1]
│   │   │       │   ├── defusedxml [required: Any, installed: 0.7.1]
│   │   │       │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
│   │   │       │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │       │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
│   │   │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
│   │   │       │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │       │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
│   │   │       │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
│   │   │       │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │       │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │       │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │       │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
│   │   │       │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │       │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │       │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │       │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │       │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │       │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
│   │   │       │   ├── nbformat [required: >=5.7, installed: 5.10.4]
│   │   │       │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │       │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │       │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │       │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │       │   ├── packaging [required: Any, installed: 24.2]
│   │   │       │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
│   │   │       │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
│   │   │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │       ├── nbformat [required: >=5.3.0, installed: 5.10.4]
│   │   │       │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │       │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │       ├── overrides [required: >=5.0, installed: 7.7.0]
│   │   │       ├── packaging [required: >=22.0, installed: 24.2]
│   │   │       ├── prometheus_client [required: >=0.9, installed: 0.22.1]
│   │   │       ├── pyzmq [required: >=24, installed: 26.4.0]
│   │   │       ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
│   │   │       ├── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │       │   ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │       │   └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │       ├── tornado [required: >=6.2.0, installed: 6.5.1]
│   │   │       ├── traitlets [required: >=5.6.0, installed: 5.14.3]
│   │   │       └── websocket-client [required: >=1.7, installed: 1.8.0]
│   │   ├── jupyter_server [required: >=2.4.0,<3, installed: 2.16.0]
│   │   │   ├── anyio [required: >=3.1.0, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
│   │   │   │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
│   │   │   │       └── cffi [required: >=1.0.1, installed: 1.17.1]
│   │   │   │           └── pycparser [required: Any, installed: 2.22]
│   │   │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
│   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
│   │   │   │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   ├── packaging [required: Any, installed: 24.2]
│   │   │   │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
│   │   │   │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
│   │   │   │   ├── referencing [required: Any, installed: 0.36.2]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
│   │   │   │   │   └── six [required: Any, installed: 1.17.0]
│   │   │   │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
│   │   │   │   └── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │   │       ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │   │       └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │   ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
│   │   │   │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
│   │   │   │   │   ├── soupsieve [required: >1.2, installed: 2.7]
│   │   │   │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
│   │   │   │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
│   │   │   │   │   └── webencodings [required: Any, installed: 0.5.1]
│   │   │   │   ├── defusedxml [required: Any, installed: 0.7.1]
│   │   │   │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
│   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
│   │   │   │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
│   │   │   │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
│   │   │   │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
│   │   │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
│   │   │   │   ├── nbformat [required: >=5.7, installed: 5.10.4]
│   │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   │   ├── packaging [required: Any, installed: 24.2]
│   │   │   │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
│   │   │   │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
│   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   ├── nbformat [required: >=5.3.0, installed: 5.10.4]
│   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   ├── overrides [required: >=5.0, installed: 7.7.0]
│   │   │   ├── packaging [required: >=22.0, installed: 24.2]
│   │   │   ├── prometheus_client [required: >=0.9, installed: 0.22.1]
│   │   │   ├── pyzmq [required: >=24, installed: 26.4.0]
│   │   │   ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
│   │   │   ├── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │   │   ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │   │   └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │   ├── tornado [required: >=6.2.0, installed: 6.5.1]
│   │   │   ├── traitlets [required: >=5.6.0, installed: 5.14.3]
│   │   │   └── websocket-client [required: >=1.7, installed: 1.8.0]
│   │   ├── jupyterlab_server [required: >=2.27.1,<3, installed: 2.27.3]
│   │   │   ├── babel [required: >=2.10, installed: 2.17.0]
│   │   │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── json5 [required: >=0.9.0, installed: 0.12.0]
│   │   │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
│   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   ├── jupyter_server [required: >=1.21,<3, installed: 2.16.0]
│   │   │   │   ├── anyio [required: >=3.1.0, installed: 4.9.0]
│   │   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   │   ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
│   │   │   │   │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
│   │   │   │   │       └── cffi [required: >=1.0.1, installed: 1.17.1]
│   │   │   │   │           └── pycparser [required: Any, installed: 2.22]
│   │   │   │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
│   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
│   │   │   │   │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   │   ├── packaging [required: Any, installed: 24.2]
│   │   │   │   │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
│   │   │   │   │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
│   │   │   │   │   ├── referencing [required: Any, installed: 0.36.2]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
│   │   │   │   │   │   └── six [required: Any, installed: 1.17.0]
│   │   │   │   │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
│   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
│   │   │   │   │   └── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │   │   │       ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │   │   │       └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │   │   ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
│   │   │   │   │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
│   │   │   │   │   │   ├── soupsieve [required: >1.2, installed: 2.7]
│   │   │   │   │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
│   │   │   │   │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
│   │   │   │   │   │   └── webencodings [required: Any, installed: 0.5.1]
│   │   │   │   │   ├── defusedxml [required: Any, installed: 0.7.1]
│   │   │   │   │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
│   │   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
│   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
│   │   │   │   │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
│   │   │   │   │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
│   │   │   │   │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   │   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   │   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
│   │   │   │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   │   │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
│   │   │   │   │   ├── nbformat [required: >=5.7, installed: 5.10.4]
│   │   │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   │   │   ├── packaging [required: Any, installed: 24.2]
│   │   │   │   │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
│   │   │   │   │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
│   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   │   ├── nbformat [required: >=5.3.0, installed: 5.10.4]
│   │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │   │   ├── overrides [required: >=5.0, installed: 7.7.0]
│   │   │   │   ├── packaging [required: >=22.0, installed: 24.2]
│   │   │   │   ├── prometheus_client [required: >=0.9, installed: 0.22.1]
│   │   │   │   ├── pyzmq [required: >=24, installed: 26.4.0]
│   │   │   │   ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
│   │   │   │   ├── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │   │   │   ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │   │   │   └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │   │   ├── tornado [required: >=6.2.0, installed: 6.5.1]
│   │   │   │   ├── traitlets [required: >=5.6.0, installed: 5.14.3]
│   │   │   │   └── websocket-client [required: >=1.7, installed: 1.8.0]
│   │   │   ├── packaging [required: >=21.3, installed: 24.2]
│   │   │   └── requests [required: >=2.31, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── notebook_shim [required: >=0.2, installed: 0.2.4]
│   │   │   └── jupyter_server [required: >=1.8,<3, installed: 2.16.0]
│   │   │       ├── anyio [required: >=3.1.0, installed: 4.9.0]
│   │   │       │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │       ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
│   │   │       │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
│   │   │       │       └── cffi [required: >=1.0.1, installed: 1.17.1]
│   │   │       │           └── pycparser [required: Any, installed: 2.22]
│   │   │       ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │   │       │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │       ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
│   │   │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │       │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │       │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │       │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
│   │   │       │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
│   │   │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │       │   ├── packaging [required: Any, installed: 24.2]
│   │   │       │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
│   │   │       │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
│   │   │       │   ├── referencing [required: Any, installed: 0.36.2]
│   │   │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
│   │   │       │   │   └── six [required: Any, installed: 1.17.0]
│   │   │       │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
│   │   │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
│   │   │       │   └── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │       │       ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │       │       └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │       ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
│   │   │       │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
│   │   │       │   │   ├── soupsieve [required: >1.2, installed: 2.7]
│   │   │       │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
│   │   │       │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
│   │   │       │   │   └── webencodings [required: Any, installed: 0.5.1]
│   │   │       │   ├── defusedxml [required: Any, installed: 0.7.1]
│   │   │       │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
│   │   │       │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │       │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
│   │   │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
│   │   │       │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │       │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
│   │   │       │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
│   │   │       │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │       │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │       │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │       │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
│   │   │       │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │       │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │       │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │       │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │       │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │       │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
│   │   │       │   ├── nbformat [required: >=5.7, installed: 5.10.4]
│   │   │       │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │       │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │       │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │       │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │       │   ├── packaging [required: Any, installed: 24.2]
│   │   │       │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
│   │   │       │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
│   │   │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │       ├── nbformat [required: >=5.3.0, installed: 5.10.4]
│   │   │       │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │       │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   │       ├── overrides [required: >=5.0, installed: 7.7.0]
│   │   │       ├── packaging [required: >=22.0, installed: 24.2]
│   │   │       ├── prometheus_client [required: >=0.9, installed: 0.22.1]
│   │   │       ├── pyzmq [required: >=24, installed: 26.4.0]
│   │   │       ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
│   │   │       ├── terminado [required: >=0.8.3, installed: 0.18.1]
│   │   │       │   ├── ptyprocess [required: Any, installed: 0.7.0]
│   │   │       │   └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │   │       ├── tornado [required: >=6.2.0, installed: 6.5.1]
│   │   │       ├── traitlets [required: >=5.6.0, installed: 5.14.3]
│   │   │       └── websocket-client [required: >=1.7, installed: 1.8.0]
│   │   ├── packaging [required: Any, installed: 24.2]
│   │   ├── setuptools [required: >=41.1.0, installed: 75.6.0]
│   │   ├── tornado [required: >=6.2.0, installed: 6.5.1]
│   │   └── traitlets [required: Any, installed: 5.14.3]
│   ├── notebook_shim [required: >=0.2,<0.3, installed: 0.2.4]
│   │   └── jupyter_server [required: >=1.8,<3, installed: 2.16.0]
│   │       ├── anyio [required: >=3.1.0, installed: 4.9.0]
│   │       │   ├── idna [required: >=2.8, installed: 3.10]
│   │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │       ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
│   │       │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
│   │       │       └── cffi [required: >=1.0.1, installed: 1.17.1]
│   │       │           └── pycparser [required: Any, installed: 2.22]
│   │       ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
│   │       │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │       ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
│   │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │       │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │       │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │       │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │       │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
│   │       │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
│   │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │       │   ├── packaging [required: Any, installed: 24.2]
│   │       │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
│   │       │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
│   │       │   ├── referencing [required: Any, installed: 0.36.2]
│   │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │       │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │       │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
│   │       │   │   └── six [required: Any, installed: 1.17.0]
│   │       │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
│   │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
│   │       │   └── terminado [required: >=0.8.3, installed: 0.18.1]
│   │       │       ├── ptyprocess [required: Any, installed: 0.7.0]
│   │       │       └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │       ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
│   │       │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
│   │       │   │   ├── soupsieve [required: >1.2, installed: 2.7]
│   │       │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
│   │       │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
│   │       │   │   └── webencodings [required: Any, installed: 0.5.1]
│   │       │   ├── defusedxml [required: Any, installed: 0.7.1]
│   │       │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
│   │       │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │       │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
│   │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
│   │       │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │       │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
│   │       │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
│   │       │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │       │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │       │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │       │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
│   │       │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │       │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │       │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │       │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │       │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │       │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │       │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │       │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │       │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │       │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
│   │       │   ├── nbformat [required: >=5.7, installed: 5.10.4]
│   │       │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │       │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │       │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │       │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │       │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │       │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │       │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │       │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │       │   ├── packaging [required: Any, installed: 24.2]
│   │       │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
│   │       │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
│   │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │       ├── nbformat [required: >=5.3.0, installed: 5.10.4]
│   │       │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │       │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │       ├── overrides [required: >=5.0, installed: 7.7.0]
│   │       ├── packaging [required: >=22.0, installed: 24.2]
│   │       ├── prometheus_client [required: >=0.9, installed: 0.22.1]
│   │       ├── pyzmq [required: >=24, installed: 26.4.0]
│   │       ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
│   │       ├── terminado [required: >=0.8.3, installed: 0.18.1]
│   │       │   ├── ptyprocess [required: Any, installed: 0.7.0]
│   │       │   └── tornado [required: >=6.1.0, installed: 6.5.1]
│   │       ├── tornado [required: >=6.2.0, installed: 6.5.1]
│   │       ├── traitlets [required: >=5.6.0, installed: 5.14.3]
│   │       └── websocket-client [required: >=1.7, installed: 1.8.0]
│   └── tornado [required: >=6.2.0, installed: 6.5.1]
├── jupyter-console [required: Any, installed: 6.6.3]
│   ├── ipykernel [required: >=6.14, installed: 6.29.5]
│   │   ├── comm [required: >=0.1.1, installed: 0.2.2]
│   │   │   └── traitlets [required: >=4, installed: 5.14.3]
│   │   ├── debugpy [required: >=1.6.5, installed: 1.8.14]
│   │   ├── ipython [required: >=7.23.1, installed: 9.3.0]
│   │   │   ├── decorator [required: Any, installed: 5.2.1]
│   │   │   ├── ipython_pygments_lexers [required: Any, installed: 1.1.1]
│   │   │   │   └── Pygments [required: Any, installed: 2.19.1]
│   │   │   ├── jedi [required: >=0.16, installed: 0.19.2]
│   │   │   │   └── parso [required: >=0.8.4,<0.9.0, installed: 0.8.4]
│   │   │   ├── matplotlib-inline [required: Any, installed: 0.1.7]
│   │   │   │   └── traitlets [required: Any, installed: 5.14.3]
│   │   │   ├── pexpect [required: >4.3, installed: 4.9.0]
│   │   │   │   └── ptyprocess [required: >=0.5, installed: 0.7.0]
│   │   │   ├── prompt_toolkit [required: >=3.0.41,<3.1.0, installed: 3.0.51]
│   │   │   │   └── wcwidth [required: Any, installed: 0.2.13]
│   │   │   ├── Pygments [required: >=2.4.0, installed: 2.19.1]
│   │   │   ├── stack-data [required: Any, installed: 0.6.3]
│   │   │   │   ├── executing [required: >=1.2.0, installed: 2.2.0]
│   │   │   │   ├── asttokens [required: >=2.1.0, installed: 3.0.0]
│   │   │   │   └── pure_eval [required: Any, installed: 0.2.3]
│   │   │   └── traitlets [required: >=5.13.0, installed: 5.14.3]
│   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── matplotlib-inline [required: >=0.1, installed: 0.1.7]
│   │   │   └── traitlets [required: Any, installed: 5.14.3]
│   │   ├── nest-asyncio [required: Any, installed: 1.6.0]
│   │   ├── packaging [required: Any, installed: 24.2]
│   │   ├── psutil [required: Any, installed: 7.0.0]
│   │   ├── pyzmq [required: >=24, installed: 26.4.0]
│   │   ├── tornado [required: >=6.1, installed: 6.5.1]
│   │   └── traitlets [required: >=5.4.0, installed: 5.14.3]
│   ├── ipython [required: Any, installed: 9.3.0]
│   │   ├── decorator [required: Any, installed: 5.2.1]
│   │   ├── ipython_pygments_lexers [required: Any, installed: 1.1.1]
│   │   │   └── Pygments [required: Any, installed: 2.19.1]
│   │   ├── jedi [required: >=0.16, installed: 0.19.2]
│   │   │   └── parso [required: >=0.8.4,<0.9.0, installed: 0.8.4]
│   │   ├── matplotlib-inline [required: Any, installed: 0.1.7]
│   │   │   └── traitlets [required: Any, installed: 5.14.3]
│   │   ├── pexpect [required: >4.3, installed: 4.9.0]
│   │   │   └── ptyprocess [required: >=0.5, installed: 0.7.0]
│   │   ├── prompt_toolkit [required: >=3.0.41,<3.1.0, installed: 3.0.51]
│   │   │   └── wcwidth [required: Any, installed: 0.2.13]
│   │   ├── Pygments [required: >=2.4.0, installed: 2.19.1]
│   │   ├── stack-data [required: Any, installed: 0.6.3]
│   │   │   ├── executing [required: >=1.2.0, installed: 2.2.0]
│   │   │   ├── asttokens [required: >=2.1.0, installed: 3.0.0]
│   │   │   └── pure_eval [required: Any, installed: 0.2.3]
│   │   └── traitlets [required: >=5.13.0, installed: 5.14.3]
│   ├── jupyter_client [required: >=7.0.0, installed: 8.6.3]
│   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   ├── prompt_toolkit [required: >=3.0.30, installed: 3.0.51]
│   │   └── wcwidth [required: Any, installed: 0.2.13]
│   ├── Pygments [required: Any, installed: 2.19.1]
│   ├── pyzmq [required: >=17, installed: 26.4.0]
│   └── traitlets [required: >=5.4, installed: 5.14.3]
├── nbconvert [required: Any, installed: 7.16.6]
│   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
│   │   ├── soupsieve [required: >1.2, installed: 2.7]
│   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
│   ├── bleach [required: !=5.0.0, installed: 6.2.0]
│   │   └── webencodings [required: Any, installed: 0.5.1]
│   ├── defusedxml [required: Any, installed: 0.7.1]
│   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
│   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
│   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
│   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
│   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
│   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
│   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   │   └── traitlets [required: >=5.4, installed: 5.14.3]
│   ├── nbformat [required: >=5.7, installed: 5.10.4]
│   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
│   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
│   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
│   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
│   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
│   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
│   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
│   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
│   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
│   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   └── traitlets [required: >=5.1, installed: 5.14.3]
│   ├── packaging [required: Any, installed: 24.2]
│   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
│   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
│   └── traitlets [required: >=5.1, installed: 5.14.3]
├── ipykernel [required: Any, installed: 6.29.5]
│   ├── comm [required: >=0.1.1, installed: 0.2.2]
│   │   └── traitlets [required: >=4, installed: 5.14.3]
│   ├── debugpy [required: >=1.6.5, installed: 1.8.14]
│   ├── ipython [required: >=7.23.1, installed: 9.3.0]
│   │   ├── decorator [required: Any, installed: 5.2.1]
│   │   ├── ipython_pygments_lexers [required: Any, installed: 1.1.1]
│   │   │   └── Pygments [required: Any, installed: 2.19.1]
│   │   ├── jedi [required: >=0.16, installed: 0.19.2]
│   │   │   └── parso [required: >=0.8.4,<0.9.0, installed: 0.8.4]
│   │   ├── matplotlib-inline [required: Any, installed: 0.1.7]
│   │   │   └── traitlets [required: Any, installed: 5.14.3]
│   │   ├── pexpect [required: >4.3, installed: 4.9.0]
│   │   │   └── ptyprocess [required: >=0.5, installed: 0.7.0]
│   │   ├── prompt_toolkit [required: >=3.0.41,<3.1.0, installed: 3.0.51]
│   │   │   └── wcwidth [required: Any, installed: 0.2.13]
│   │   ├── Pygments [required: >=2.4.0, installed: 2.19.1]
│   │   ├── stack-data [required: Any, installed: 0.6.3]
│   │   │   ├── executing [required: >=1.2.0, installed: 2.2.0]
│   │   │   ├── asttokens [required: >=2.1.0, installed: 3.0.0]
│   │   │   └── pure_eval [required: Any, installed: 0.2.3]
│   │   └── traitlets [required: >=5.13.0, installed: 5.14.3]
│   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
│   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
│   │   ├── tornado [required: >=6.2, installed: 6.5.1]
│   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
│   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
│   │   └── traitlets [required: >=5.3, installed: 5.14.3]
│   ├── matplotlib-inline [required: >=0.1, installed: 0.1.7]
│   │   └── traitlets [required: Any, installed: 5.14.3]
│   ├── nest-asyncio [required: Any, installed: 1.6.0]
│   ├── packaging [required: Any, installed: 24.2]
│   ├── psutil [required: Any, installed: 7.0.0]
│   ├── pyzmq [required: >=24, installed: 26.4.0]
│   ├── tornado [required: >=6.1, installed: 6.5.1]
│   └── traitlets [required: >=5.4.0, installed: 5.14.3]
├── ipywidgets [required: Any, installed: 8.1.7]
│   ├── comm [required: >=0.1.3, installed: 0.2.2]
│   │   └── traitlets [required: >=4, installed: 5.14.3]
│   ├── ipython [required: >=6.1.0, installed: 9.3.0]
│   │   ├── decorator [required: Any, installed: 5.2.1]
│   │   ├── ipython_pygments_lexers [required: Any, installed: 1.1.1]
│   │   │   └── Pygments [required: Any, installed: 2.19.1]
│   │   ├── jedi [required: >=0.16, installed: 0.19.2]
│   │   │   └── parso [required: >=0.8.4,<0.9.0, installed: 0.8.4]
│   │   ├── matplotlib-inline [required: Any, installed: 0.1.7]
│   │   │   └── traitlets [required: Any, installed: 5.14.3]
│   │   ├── pexpect [required: >4.3, installed: 4.9.0]
│   │   │   └── ptyprocess [required: >=0.5, installed: 0.7.0]
│   │   ├── prompt_toolkit [required: >=3.0.41,<3.1.0, installed: 3.0.51]
│   │   │   └── wcwidth [required: Any, installed: 0.2.13]
│   │   ├── Pygments [required: >=2.4.0, installed: 2.19.1]
│   │   ├── stack-data [required: Any, installed: 0.6.3]
│   │   │   ├── executing [required: >=1.2.0, installed: 2.2.0]
│   │   │   ├── asttokens [required: >=2.1.0, installed: 3.0.0]
│   │   │   └── pure_eval [required: Any, installed: 0.2.3]
│   │   └── traitlets [required: >=5.13.0, installed: 5.14.3]
│   ├── traitlets [required: >=4.3.1, installed: 5.14.3]
│   ├── widgetsnbextension [required: ~=4.0.14, installed: 4.0.14]
│   └── jupyterlab_widgets [required: ~=3.0.15, installed: 3.0.15]
└── jupyterlab [required: Any, installed: 4.4.3]
    ├── async-lru [required: >=1.0.0, installed: 2.0.5]
    ├── httpx [required: >=0.25.0, installed: 0.28.1]
    │   ├── anyio [required: Any, installed: 4.9.0]
    │   │   ├── idna [required: >=2.8, installed: 3.10]
    │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
    │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
    │   ├── certifi [required: Any, installed: 2025.4.26]
    │   ├── httpcore [required: ==1.*, installed: 1.0.9]
    │   │   ├── certifi [required: Any, installed: 2025.4.26]
    │   │   └── h11 [required: >=0.16, installed: 0.16.0]
    │   └── idna [required: Any, installed: 3.10]
    ├── ipykernel [required: >=6.5.0, installed: 6.29.5]
    │   ├── comm [required: >=0.1.1, installed: 0.2.2]
    │   │   └── traitlets [required: >=4, installed: 5.14.3]
    │   ├── debugpy [required: >=1.6.5, installed: 1.8.14]
    │   ├── ipython [required: >=7.23.1, installed: 9.3.0]
    │   │   ├── decorator [required: Any, installed: 5.2.1]
    │   │   ├── ipython_pygments_lexers [required: Any, installed: 1.1.1]
    │   │   │   └── Pygments [required: Any, installed: 2.19.1]
    │   │   ├── jedi [required: >=0.16, installed: 0.19.2]
    │   │   │   └── parso [required: >=0.8.4,<0.9.0, installed: 0.8.4]
    │   │   ├── matplotlib-inline [required: Any, installed: 0.1.7]
    │   │   │   └── traitlets [required: Any, installed: 5.14.3]
    │   │   ├── pexpect [required: >4.3, installed: 4.9.0]
    │   │   │   └── ptyprocess [required: >=0.5, installed: 0.7.0]
    │   │   ├── prompt_toolkit [required: >=3.0.41,<3.1.0, installed: 3.0.51]
    │   │   │   └── wcwidth [required: Any, installed: 0.2.13]
    │   │   ├── Pygments [required: >=2.4.0, installed: 2.19.1]
    │   │   ├── stack-data [required: Any, installed: 0.6.3]
    │   │   │   ├── executing [required: >=1.2.0, installed: 2.2.0]
    │   │   │   ├── asttokens [required: >=2.1.0, installed: 3.0.0]
    │   │   │   └── pure_eval [required: Any, installed: 0.2.3]
    │   │   └── traitlets [required: >=5.13.0, installed: 5.14.3]
    │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
    │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
    │   │   │   └── six [required: >=1.5, installed: 1.17.0]
    │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
    │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
    │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   ├── matplotlib-inline [required: >=0.1, installed: 0.1.7]
    │   │   └── traitlets [required: Any, installed: 5.14.3]
    │   ├── nest-asyncio [required: Any, installed: 1.6.0]
    │   ├── packaging [required: Any, installed: 24.2]
    │   ├── psutil [required: Any, installed: 7.0.0]
    │   ├── pyzmq [required: >=24, installed: 26.4.0]
    │   ├── tornado [required: >=6.1, installed: 6.5.1]
    │   └── traitlets [required: >=5.4.0, installed: 5.14.3]
    ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
    │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    ├── jupyter_core [required: Any, installed: 5.8.1]
    │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   └── traitlets [required: >=5.3, installed: 5.14.3]
    ├── jupyter-lsp [required: >=2.0.0, installed: 2.2.5]
    │   └── jupyter_server [required: >=1.1.2, installed: 2.16.0]
    │       ├── anyio [required: >=3.1.0, installed: 4.9.0]
    │       │   ├── idna [required: >=2.8, installed: 3.10]
    │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
    │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
    │       ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
    │       │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
    │       │       └── cffi [required: >=1.0.1, installed: 1.17.1]
    │       │           └── pycparser [required: Any, installed: 2.22]
    │       ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
    │       │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │       ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
    │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
    │       │   │   └── six [required: >=1.5, installed: 1.17.0]
    │       │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
    │       │   ├── tornado [required: >=6.2, installed: 6.5.1]
    │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
    │       │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
    │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │       │   ├── packaging [required: Any, installed: 24.2]
    │       │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
    │       │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
    │       │   ├── referencing [required: Any, installed: 0.36.2]
    │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
    │       │   │   └── six [required: Any, installed: 1.17.0]
    │       │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
    │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
    │       │   └── terminado [required: >=0.8.3, installed: 0.18.1]
    │       │       ├── ptyprocess [required: Any, installed: 0.7.0]
    │       │       └── tornado [required: >=6.1.0, installed: 6.5.1]
    │       ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
    │       │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
    │       │   │   ├── soupsieve [required: >1.2, installed: 2.7]
    │       │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
    │       │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
    │       │   │   └── webencodings [required: Any, installed: 0.5.1]
    │       │   ├── defusedxml [required: Any, installed: 0.7.1]
    │       │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
    │       │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │       │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
    │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
    │       │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │       │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
    │       │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
    │       │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
    │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
    │       │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
    │       │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
    │       │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
    │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
    │       │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │       │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │       │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │       │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │       │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │       │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
    │       │   ├── nbformat [required: >=5.7, installed: 5.10.4]
    │       │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │       │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │       │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │       │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │       │   ├── packaging [required: Any, installed: 24.2]
    │       │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
    │       │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
    │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │       ├── nbformat [required: >=5.3.0, installed: 5.10.4]
    │       │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │       │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │       ├── overrides [required: >=5.0, installed: 7.7.0]
    │       ├── packaging [required: >=22.0, installed: 24.2]
    │       ├── prometheus_client [required: >=0.9, installed: 0.22.1]
    │       ├── pyzmq [required: >=24, installed: 26.4.0]
    │       ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
    │       ├── terminado [required: >=0.8.3, installed: 0.18.1]
    │       │   ├── ptyprocess [required: Any, installed: 0.7.0]
    │       │   └── tornado [required: >=6.1.0, installed: 6.5.1]
    │       ├── tornado [required: >=6.2.0, installed: 6.5.1]
    │       ├── traitlets [required: >=5.6.0, installed: 5.14.3]
    │       └── websocket-client [required: >=1.7, installed: 1.8.0]
    ├── jupyter_server [required: >=2.4.0,<3, installed: 2.16.0]
    │   ├── anyio [required: >=3.1.0, installed: 4.9.0]
    │   │   ├── idna [required: >=2.8, installed: 3.10]
    │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
    │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
    │   ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
    │   │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
    │   │       └── cffi [required: >=1.0.1, installed: 1.17.1]
    │   │           └── pycparser [required: Any, installed: 2.22]
    │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
    │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │   ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
    │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
    │   │   │   └── six [required: >=1.5, installed: 1.17.0]
    │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
    │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
    │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
    │   │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
    │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │   │   ├── packaging [required: Any, installed: 24.2]
    │   │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
    │   │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
    │   │   ├── referencing [required: Any, installed: 0.36.2]
    │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
    │   │   │   └── six [required: Any, installed: 1.17.0]
    │   │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
    │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
    │   │   └── terminado [required: >=0.8.3, installed: 0.18.1]
    │   │       ├── ptyprocess [required: Any, installed: 0.7.0]
    │   │       └── tornado [required: >=6.1.0, installed: 6.5.1]
    │   ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
    │   │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
    │   │   │   ├── soupsieve [required: >1.2, installed: 2.7]
    │   │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
    │   │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
    │   │   │   └── webencodings [required: Any, installed: 0.5.1]
    │   │   ├── defusedxml [required: Any, installed: 0.7.1]
    │   │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
    │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │   │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
    │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
    │   │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │   │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
    │   │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
    │   │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
    │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
    │   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
    │   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
    │   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
    │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
    │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │   │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
    │   │   ├── nbformat [required: >=5.7, installed: 5.10.4]
    │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │   │   ├── packaging [required: Any, installed: 24.2]
    │   │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
    │   │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
    │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │   ├── nbformat [required: >=5.3.0, installed: 5.10.4]
    │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │   ├── overrides [required: >=5.0, installed: 7.7.0]
    │   ├── packaging [required: >=22.0, installed: 24.2]
    │   ├── prometheus_client [required: >=0.9, installed: 0.22.1]
    │   ├── pyzmq [required: >=24, installed: 26.4.0]
    │   ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
    │   ├── terminado [required: >=0.8.3, installed: 0.18.1]
    │   │   ├── ptyprocess [required: Any, installed: 0.7.0]
    │   │   └── tornado [required: >=6.1.0, installed: 6.5.1]
    │   ├── tornado [required: >=6.2.0, installed: 6.5.1]
    │   ├── traitlets [required: >=5.6.0, installed: 5.14.3]
    │   └── websocket-client [required: >=1.7, installed: 1.8.0]
    ├── jupyterlab_server [required: >=2.27.1,<3, installed: 2.27.3]
    │   ├── babel [required: >=2.10, installed: 2.17.0]
    │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
    │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │   ├── json5 [required: >=0.9.0, installed: 0.12.0]
    │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
    │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │   ├── jupyter_server [required: >=1.21,<3, installed: 2.16.0]
    │   │   ├── anyio [required: >=3.1.0, installed: 4.9.0]
    │   │   │   ├── idna [required: >=2.8, installed: 3.10]
    │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
    │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
    │   │   ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
    │   │   │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
    │   │   │       └── cffi [required: >=1.0.1, installed: 1.17.1]
    │   │   │           └── pycparser [required: Any, installed: 2.22]
    │   │   ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
    │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │   │   ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
    │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
    │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
    │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
    │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
    │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
    │   │   │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
    │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │   │   │   ├── packaging [required: Any, installed: 24.2]
    │   │   │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
    │   │   │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
    │   │   │   ├── referencing [required: Any, installed: 0.36.2]
    │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
    │   │   │   │   └── six [required: Any, installed: 1.17.0]
    │   │   │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
    │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
    │   │   │   └── terminado [required: >=0.8.3, installed: 0.18.1]
    │   │   │       ├── ptyprocess [required: Any, installed: 0.7.0]
    │   │   │       └── tornado [required: >=6.1.0, installed: 6.5.1]
    │   │   ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
    │   │   │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
    │   │   │   │   ├── soupsieve [required: >1.2, installed: 2.7]
    │   │   │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
    │   │   │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
    │   │   │   │   └── webencodings [required: Any, installed: 0.5.1]
    │   │   │   ├── defusedxml [required: Any, installed: 0.7.1]
    │   │   │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
    │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │   │   │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
    │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
    │   │   │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │   │   │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
    │   │   │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
    │   │   │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
    │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
    │   │   │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
    │   │   │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
    │   │   │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
    │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
    │   │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │   │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │   │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │   │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │   │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │   │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │   │   │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
    │   │   │   ├── nbformat [required: >=5.7, installed: 5.10.4]
    │   │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │   │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │   │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │   │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │   │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │   │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │   │   │   ├── packaging [required: Any, installed: 24.2]
    │   │   │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
    │   │   │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
    │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │   │   ├── nbformat [required: >=5.3.0, installed: 5.10.4]
    │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │   │   ├── overrides [required: >=5.0, installed: 7.7.0]
    │   │   ├── packaging [required: >=22.0, installed: 24.2]
    │   │   ├── prometheus_client [required: >=0.9, installed: 0.22.1]
    │   │   ├── pyzmq [required: >=24, installed: 26.4.0]
    │   │   ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
    │   │   ├── terminado [required: >=0.8.3, installed: 0.18.1]
    │   │   │   ├── ptyprocess [required: Any, installed: 0.7.0]
    │   │   │   └── tornado [required: >=6.1.0, installed: 6.5.1]
    │   │   ├── tornado [required: >=6.2.0, installed: 6.5.1]
    │   │   ├── traitlets [required: >=5.6.0, installed: 5.14.3]
    │   │   └── websocket-client [required: >=1.7, installed: 1.8.0]
    │   ├── packaging [required: >=21.3, installed: 24.2]
    │   └── requests [required: >=2.31, installed: 2.32.3]
    │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
    │       ├── idna [required: >=2.5,<4, installed: 3.10]
    │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
    │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
    ├── notebook_shim [required: >=0.2, installed: 0.2.4]
    │   └── jupyter_server [required: >=1.8,<3, installed: 2.16.0]
    │       ├── anyio [required: >=3.1.0, installed: 4.9.0]
    │       │   ├── idna [required: >=2.8, installed: 3.10]
    │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
    │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
    │       ├── argon2-cffi [required: >=21.1, installed: 25.1.0]
    │       │   └── argon2-cffi-bindings [required: Any, installed: 21.2.0]
    │       │       └── cffi [required: >=1.0.1, installed: 1.17.1]
    │       │           └── pycparser [required: Any, installed: 2.22]
    │       ├── Jinja2 [required: >=3.0.3, installed: 3.1.6]
    │       │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │       ├── jupyter_client [required: >=7.4.4, installed: 8.6.3]
    │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
    │       │   │   └── six [required: >=1.5, installed: 1.17.0]
    │       │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
    │       │   ├── tornado [required: >=6.2, installed: 6.5.1]
    │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       ├── jupyter-events [required: >=0.11.0, installed: 0.12.0]
    │       │   ├── jsonschema [required: >=4.18.0, installed: 4.24.0]
    │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │       │   ├── packaging [required: Any, installed: 24.2]
    │       │   ├── python-json-logger [required: >=2.0.4, installed: 3.3.0]
    │       │   ├── PyYAML [required: >=5.3, installed: 6.0.2]
    │       │   ├── referencing [required: Any, installed: 0.36.2]
    │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   ├── rfc3339-validator [required: Any, installed: 0.1.4]
    │       │   │   └── six [required: Any, installed: 1.17.0]
    │       │   ├── rfc3986-validator [required: >=0.1.1, installed: 0.1.1]
    │       │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       ├── jupyter_server_terminals [required: >=0.4.4, installed: 0.5.3]
    │       │   └── terminado [required: >=0.8.3, installed: 0.18.1]
    │       │       ├── ptyprocess [required: Any, installed: 0.7.0]
    │       │       └── tornado [required: >=6.1.0, installed: 6.5.1]
    │       ├── nbconvert [required: >=6.4.4, installed: 7.16.6]
    │       │   ├── beautifulsoup4 [required: Any, installed: 4.13.4]
    │       │   │   ├── soupsieve [required: >1.2, installed: 2.7]
    │       │   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
    │       │   ├── bleach [required: !=5.0.0, installed: 6.2.0]
    │       │   │   └── webencodings [required: Any, installed: 0.5.1]
    │       │   ├── defusedxml [required: Any, installed: 0.7.1]
    │       │   ├── Jinja2 [required: >=3.0, installed: 3.1.6]
    │       │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │       │   ├── jupyter_core [required: >=4.7, installed: 5.8.1]
    │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   ├── jupyterlab_pygments [required: Any, installed: 0.3.0]
    │       │   ├── MarkupSafe [required: >=2.0, installed: 3.0.2]
    │       │   ├── mistune [required: >=2.0.3,<4, installed: 3.1.3]
    │       │   ├── nbclient [required: >=0.5.0, installed: 0.10.2]
    │       │   │   ├── jupyter_client [required: >=6.1.12, installed: 8.6.3]
    │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
    │       │   │   │   │   └── six [required: >=1.5, installed: 1.17.0]
    │       │   │   │   ├── pyzmq [required: >=23.0, installed: 26.4.0]
    │       │   │   │   ├── tornado [required: >=6.2, installed: 6.5.1]
    │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   ├── nbformat [required: >=5.1, installed: 5.10.4]
    │       │   │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │       │   │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │       │   │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │       │   │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │       │   │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │       │   │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │       │   │   └── traitlets [required: >=5.4, installed: 5.14.3]
    │       │   ├── nbformat [required: >=5.7, installed: 5.10.4]
    │       │   │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │       │   │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │       │   │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │       │   │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │       │   │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │       │   │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │       │   ├── packaging [required: Any, installed: 24.2]
    │       │   ├── pandocfilters [required: >=1.4.1, installed: 1.5.1]
    │       │   ├── Pygments [required: >=2.4.1, installed: 2.19.1]
    │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │       ├── nbformat [required: >=5.3.0, installed: 5.10.4]
    │       │   ├── fastjsonschema [required: >=2.15, installed: 2.21.1]
    │       │   ├── jsonschema [required: >=2.6, installed: 4.24.0]
    │       │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   ├── jsonschema-specifications [required: >=2023.03.6, installed: 2025.4.1]
    │       │   │   │   └── referencing [required: >=0.31.0, installed: 0.36.2]
    │       │   │   │       ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │       ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │       └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   ├── referencing [required: >=0.28.4, installed: 0.36.2]
    │       │   │   │   ├── attrs [required: >=22.2.0, installed: 25.3.0]
    │       │   │   │   ├── rpds-py [required: >=0.7.0, installed: 0.25.1]
    │       │   │   │   └── typing_extensions [required: >=4.4.0, installed: 4.14.0]
    │       │   │   └── rpds-py [required: >=0.7.1, installed: 0.25.1]
    │       │   ├── jupyter_core [required: >=4.12,!=5.0.*, installed: 5.8.1]
    │       │   │   ├── platformdirs [required: >=2.5, installed: 4.3.8]
    │       │   │   └── traitlets [required: >=5.3, installed: 5.14.3]
    │       │   └── traitlets [required: >=5.1, installed: 5.14.3]
    │       ├── overrides [required: >=5.0, installed: 7.7.0]
    │       ├── packaging [required: >=22.0, installed: 24.2]
    │       ├── prometheus_client [required: >=0.9, installed: 0.22.1]
    │       ├── pyzmq [required: >=24, installed: 26.4.0]
    │       ├── Send2Trash [required: >=1.8.2, installed: 1.8.3]
    │       ├── terminado [required: >=0.8.3, installed: 0.18.1]
    │       │   ├── ptyprocess [required: Any, installed: 0.7.0]
    │       │   └── tornado [required: >=6.1.0, installed: 6.5.1]
    │       ├── tornado [required: >=6.2.0, installed: 6.5.1]
    │       ├── traitlets [required: >=5.6.0, installed: 5.14.3]
    │       └── websocket-client [required: >=1.7, installed: 1.8.0]
    ├── packaging [required: Any, installed: 24.2]
    ├── setuptools [required: >=41.1.0, installed: 75.6.0]
    ├── tornado [required: >=6.2.0, installed: 6.5.1]
    └── traitlets [required: Any, installed: 5.14.3]
langchain==0.3.25
├── langchain-core [required: >=0.3.58,<1.0.0, installed: 0.3.63]
│   ├── langsmith [required: >=0.1.126,<0.4, installed: 0.3.44]
│   │   ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── orjson [required: >=3.9.14,<4.0.0, installed: 3.10.18]
│   │   ├── packaging [required: >=23.2, installed: 24.2]
│   │   ├── pydantic [required: >=2.7.4,<3.0.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── requests [required: >=2,<3, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── requests-toolbelt [required: >=1.0.0,<2.0.0, installed: 1.0.0]
│   │   │   └── requests [required: >=2.0.1,<3.0.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   └── zstandard [required: >=0.23.0,<0.24.0, installed: 0.23.0]
│   ├── tenacity [required: >=8.1.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   ├── jsonpatch [required: >=1.33,<2.0, installed: 1.33]
│   │   └── jsonpointer [required: >=1.9, installed: 3.0.0]
│   ├── PyYAML [required: >=5.3, installed: 6.0.2]
│   ├── packaging [required: >=23.2,<25, installed: 24.2]
│   ├── typing_extensions [required: >=4.7, installed: 4.14.0]
│   └── pydantic [required: >=2.7.4, installed: 2.11.5]
│       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
├── langchain-text-splitters [required: >=0.3.8,<1.0.0, installed: 0.3.8]
│   └── langchain-core [required: >=0.3.51,<1.0.0, installed: 0.3.63]
│       ├── langsmith [required: >=0.1.126,<0.4, installed: 0.3.44]
│       │   ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│       │   │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   │   └── idna [required: Any, installed: 3.10]
│       │   ├── orjson [required: >=3.9.14,<4.0.0, installed: 3.10.18]
│       │   ├── packaging [required: >=23.2, installed: 24.2]
│       │   ├── pydantic [required: >=2.7.4,<3.0.0, installed: 2.11.5]
│       │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   ├── requests [required: >=2,<3, installed: 2.32.3]
│       │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   ├── requests-toolbelt [required: >=1.0.0,<2.0.0, installed: 1.0.0]
│       │   │   └── requests [required: >=2.0.1,<3.0.0, installed: 2.32.3]
│       │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   └── zstandard [required: >=0.23.0,<0.24.0, installed: 0.23.0]
│       ├── tenacity [required: >=8.1.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│       ├── jsonpatch [required: >=1.33,<2.0, installed: 1.33]
│       │   └── jsonpointer [required: >=1.9, installed: 3.0.0]
│       ├── PyYAML [required: >=5.3, installed: 6.0.2]
│       ├── packaging [required: >=23.2,<25, installed: 24.2]
│       ├── typing_extensions [required: >=4.7, installed: 4.14.0]
│       └── pydantic [required: >=2.7.4, installed: 2.11.5]
│           ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│               └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
├── langsmith [required: >=0.1.17,<0.4, installed: 0.3.44]
│   ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   └── idna [required: Any, installed: 3.10]
│   ├── orjson [required: >=3.9.14,<4.0.0, installed: 3.10.18]
│   ├── packaging [required: >=23.2, installed: 24.2]
│   ├── pydantic [required: >=2.7.4,<3.0.0, installed: 2.11.5]
│   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   ├── requests [required: >=2,<3, installed: 2.32.3]
│   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   ├── requests-toolbelt [required: >=1.0.0,<2.0.0, installed: 1.0.0]
│   │   └── requests [required: >=2.0.1,<3.0.0, installed: 2.32.3]
│   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   └── zstandard [required: >=0.23.0,<0.24.0, installed: 0.23.0]
├── pydantic [required: >=2.7.4,<3.0.0, installed: 2.11.5]
│   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
├── SQLAlchemy [required: >=1.4,<3, installed: 2.0.41]
│   ├── greenlet [required: >=1, installed: 3.2.2]
│   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
├── requests [required: >=2,<3, installed: 2.32.3]
│   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   ├── idna [required: >=2.5,<4, installed: 3.10]
│   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
└── PyYAML [required: >=5.3, installed: 6.0.2]
llama-index==0.12.40
├── llama-index-agent-openai [required: >=0.4.0,<0.5, installed: 0.4.9]
│   ├── llama-index-core [required: >=0.12.18,<0.13, installed: 0.12.40]
│   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── wrapt [required: Any, installed: 1.17.2]
│   ├── llama-index-llms-openai [required: >=0.4.0,<0.5, installed: 0.4.2]
│   │   ├── llama-index-core [required: >=0.12.37,<0.13, installed: 0.12.40]
│   │   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   │   └── idna [required: Any, installed: 3.10]
│   │   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   └── wrapt [required: Any, installed: 1.17.2]
│   │   └── openai [required: >=1.81.0,<2, installed: 1.84.0]
│   │       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│   │       │   ├── idna [required: >=2.8, installed: 3.10]
│   │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│   │       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│   │       │   ├── anyio [required: Any, installed: 4.9.0]
│   │       │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │       │   ├── certifi [required: Any, installed: 2025.4.26]
│   │       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │       │   └── idna [required: Any, installed: 3.10]
│   │       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│   │       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│   │       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │       ├── sniffio [required: Any, installed: 1.3.1]
│   │       ├── tqdm [required: >4, installed: 4.67.1]
│   │       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
│   └── openai [required: >=1.14.0, installed: 1.84.0]
│       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│       │   ├── idna [required: >=2.8, installed: 3.10]
│       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│       │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   └── idna [required: Any, installed: 3.10]
│       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       ├── sniffio [required: Any, installed: 1.3.1]
│       ├── tqdm [required: >4, installed: 4.67.1]
│       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
├── llama-index-cli [required: >=0.4.2,<0.5, installed: 0.4.2]
│   ├── llama-index-core [required: >=0.12.0,<0.13.0, installed: 0.12.40]
│   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── wrapt [required: Any, installed: 1.17.2]
│   ├── llama-index-embeddings-openai [required: >=0.3.0,<0.4.0, installed: 0.3.1]
│   │   ├── llama-index-core [required: >=0.12.0,<0.13.0, installed: 0.12.40]
│   │   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   │   └── idna [required: Any, installed: 3.10]
│   │   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   └── wrapt [required: Any, installed: 1.17.2]
│   │   └── openai [required: >=1.1.0, installed: 1.84.0]
│   │       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│   │       │   ├── idna [required: >=2.8, installed: 3.10]
│   │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│   │       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│   │       │   ├── anyio [required: Any, installed: 4.9.0]
│   │       │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │       │   ├── certifi [required: Any, installed: 2025.4.26]
│   │       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │       │   └── idna [required: Any, installed: 3.10]
│   │       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│   │       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│   │       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │       ├── sniffio [required: Any, installed: 1.3.1]
│   │       ├── tqdm [required: >4, installed: 4.67.1]
│   │       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
│   └── llama-index-llms-openai [required: >=0.4.0,<0.5.0, installed: 0.4.2]
│       ├── llama-index-core [required: >=0.12.37,<0.13, installed: 0.12.40]
│       │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│       │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│       │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│       │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│       │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│       │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│       │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│       │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│       │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│       │   │       ├── idna [required: >=2.0, installed: 3.10]
│       │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│       │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│       │   ├── aiosqlite [required: Any, installed: 0.21.0]
│       │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│       │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│       │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│       │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   ├── griffe [required: Any, installed: 1.7.3]
│       │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│       │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│       │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│       │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│       │   │   └── pydantic [required: Any, installed: 2.11.5]
│       │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│       │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│       │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│       │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│       │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│       │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│       │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│       │   ├── httpx [required: Any, installed: 0.28.1]
│       │   │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   │   └── idna [required: Any, installed: 3.10]
│       │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│       │   ├── networkx [required: >=3.0, installed: 3.5]
│       │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│       │   │   ├── click [required: Any, installed: 8.2.1]
│       │   │   ├── joblib [required: Any, installed: 1.5.1]
│       │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│       │   │   └── tqdm [required: Any, installed: 4.67.1]
│       │   ├── numpy [required: Any, installed: 2.2.6]
│       │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│       │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│       │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│       │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│       │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│       │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│       │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│       │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│       │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│       │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│       │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│       │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│       │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│       │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│       │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   └── wrapt [required: Any, installed: 1.17.2]
│       └── openai [required: >=1.81.0,<2, installed: 1.84.0]
│           ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│           │   ├── idna [required: >=2.8, installed: 3.10]
│           │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│           │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│           ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│           ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│           │   ├── anyio [required: Any, installed: 4.9.0]
│           │   │   ├── idna [required: >=2.8, installed: 3.10]
│           │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│           │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│           │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│           │   │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│           │   └── idna [required: Any, installed: 3.10]
│           ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│           ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│           │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│           ├── sniffio [required: Any, installed: 1.3.1]
│           ├── tqdm [required: >4, installed: 4.67.1]
│           └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
├── llama-index-core [required: >=0.12.40,<0.13, installed: 0.12.40]
│   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │       ├── idna [required: >=2.0, installed: 3.10]
│   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   └── pydantic [required: Any, installed: 2.11.5]
│   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   ├── httpx [required: Any, installed: 0.28.1]
│   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   └── idna [required: Any, installed: 3.10]
│   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   ├── networkx [required: >=3.0, installed: 3.5]
│   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   ├── click [required: Any, installed: 8.2.1]
│   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   └── tqdm [required: Any, installed: 4.67.1]
│   ├── numpy [required: Any, installed: 2.2.6]
│   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   └── wrapt [required: Any, installed: 1.17.2]
├── llama-index-embeddings-openai [required: >=0.3.0,<0.4, installed: 0.3.1]
│   ├── llama-index-core [required: >=0.12.0,<0.13.0, installed: 0.12.40]
│   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── wrapt [required: Any, installed: 1.17.2]
│   └── openai [required: >=1.1.0, installed: 1.84.0]
│       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│       │   ├── idna [required: >=2.8, installed: 3.10]
│       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│       │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   └── idna [required: Any, installed: 3.10]
│       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       ├── sniffio [required: Any, installed: 1.3.1]
│       ├── tqdm [required: >4, installed: 4.67.1]
│       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
├── llama-index-indices-managed-llama-cloud [required: >=0.4.0, installed: 0.7.3]
│   ├── llama-cloud [required: ==0.1.23, installed: 0.1.23]
│   │   ├── certifi [required: >=2024.7.4, installed: 2025.4.26]
│   │   ├── httpx [required: >=0.20.0, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   └── pydantic [required: >=1.10, installed: 2.11.5]
│   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   └── llama-index-core [required: >=0.12.0,<0.13, installed: 0.12.40]
│       ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│       │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│       │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│       │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│       │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│       │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│       │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│       │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│       │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│       │       ├── idna [required: >=2.0, installed: 3.10]
│       │       ├── multidict [required: >=4.0, installed: 6.4.4]
│       │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│       ├── aiosqlite [required: Any, installed: 0.21.0]
│       │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│       ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│       │   ├── Deprecated [required: Any, installed: 1.2.18]
│       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   ├── griffe [required: Any, installed: 1.7.3]
│       │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│       │   ├── Jinja2 [required: Any, installed: 3.1.6]
│       │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│       │   ├── platformdirs [required: Any, installed: 4.3.8]
│       │   └── pydantic [required: Any, installed: 2.11.5]
│       │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       ├── dataclasses-json [required: Any, installed: 0.6.7]
│       │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│       │   │   └── packaging [required: >=17.0, installed: 24.2]
│       │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│       │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│       │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│       ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│       ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│       ├── httpx [required: Any, installed: 0.28.1]
│       │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   └── idna [required: Any, installed: 3.10]
│       ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│       ├── networkx [required: >=3.0, installed: 3.5]
│       ├── nltk [required: >3.8.1, installed: 3.9.1]
│       │   ├── click [required: Any, installed: 8.2.1]
│       │   ├── joblib [required: Any, installed: 1.5.1]
│       │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│       │   └── tqdm [required: Any, installed: 4.67.1]
│       ├── numpy [required: Any, installed: 2.2.6]
│       ├── pillow [required: >=9.0.0, installed: 11.2.1]
│       ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│       ├── requests [required: >=2.31.0, installed: 2.32.3]
│       │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│       │   ├── greenlet [required: >=1, installed: 3.2.2]
│       │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│       ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│       ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│       │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│       │   └── requests [required: >=2.26.0, installed: 2.32.3]
│       │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │       ├── idna [required: >=2.5,<4, installed: 3.10]
│       │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│       ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│       ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│       │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       └── wrapt [required: Any, installed: 1.17.2]
├── llama-index-llms-openai [required: >=0.4.0,<0.5, installed: 0.4.2]
│   ├── llama-index-core [required: >=0.12.37,<0.13, installed: 0.12.40]
│   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── wrapt [required: Any, installed: 1.17.2]
│   └── openai [required: >=1.81.0,<2, installed: 1.84.0]
│       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│       │   ├── idna [required: >=2.8, installed: 3.10]
│       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│       │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   └── idna [required: Any, installed: 3.10]
│       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       ├── sniffio [required: Any, installed: 1.3.1]
│       ├── tqdm [required: >4, installed: 4.67.1]
│       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
├── llama-index-multi-modal-llms-openai [required: >=0.5.0,<0.6, installed: 0.5.1]
│   ├── llama-index-core [required: >=0.12.3,<0.13, installed: 0.12.40]
│   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── wrapt [required: Any, installed: 1.17.2]
│   └── llama-index-llms-openai [required: >=0.4.0,<0.5, installed: 0.4.2]
│       ├── llama-index-core [required: >=0.12.37,<0.13, installed: 0.12.40]
│       │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│       │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│       │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│       │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│       │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│       │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│       │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│       │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│       │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│       │   │       ├── idna [required: >=2.0, installed: 3.10]
│       │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│       │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│       │   ├── aiosqlite [required: Any, installed: 0.21.0]
│       │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│       │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│       │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│       │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   ├── griffe [required: Any, installed: 1.7.3]
│       │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│       │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│       │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│       │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│       │   │   └── pydantic [required: Any, installed: 2.11.5]
│       │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│       │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│       │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│       │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│       │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│       │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│       │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│       │   ├── httpx [required: Any, installed: 0.28.1]
│       │   │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   │   └── idna [required: Any, installed: 3.10]
│       │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│       │   ├── networkx [required: >=3.0, installed: 3.5]
│       │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│       │   │   ├── click [required: Any, installed: 8.2.1]
│       │   │   ├── joblib [required: Any, installed: 1.5.1]
│       │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│       │   │   └── tqdm [required: Any, installed: 4.67.1]
│       │   ├── numpy [required: Any, installed: 2.2.6]
│       │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│       │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│       │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│       │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│       │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│       │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│       │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│       │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│       │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│       │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│       │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│       │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│       │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│       │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│       │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   └── wrapt [required: Any, installed: 1.17.2]
│       └── openai [required: >=1.81.0,<2, installed: 1.84.0]
│           ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│           │   ├── idna [required: >=2.8, installed: 3.10]
│           │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│           │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│           ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│           ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│           │   ├── anyio [required: Any, installed: 4.9.0]
│           │   │   ├── idna [required: >=2.8, installed: 3.10]
│           │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│           │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│           │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│           │   │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│           │   └── idna [required: Any, installed: 3.10]
│           ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│           ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│           │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│           ├── sniffio [required: Any, installed: 1.3.1]
│           ├── tqdm [required: >4, installed: 4.67.1]
│           └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
├── llama-index-program-openai [required: >=0.3.0,<0.4, installed: 0.3.2]
│   ├── llama-index-agent-openai [required: >=0.4.0,<0.5, installed: 0.4.9]
│   │   ├── llama-index-core [required: >=0.12.18,<0.13, installed: 0.12.40]
│   │   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   │   └── idna [required: Any, installed: 3.10]
│   │   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   └── wrapt [required: Any, installed: 1.17.2]
│   │   ├── llama-index-llms-openai [required: >=0.4.0,<0.5, installed: 0.4.2]
│   │   │   ├── llama-index-core [required: >=0.12.37,<0.13, installed: 0.12.40]
│   │   │   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   │   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   │   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   │   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   │   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   │   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   │   │   └── idna [required: Any, installed: 3.10]
│   │   │   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   │   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   │   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   │   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   │   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   │   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   │   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   │   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   │   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   │   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   │   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   │   └── wrapt [required: Any, installed: 1.17.2]
│   │   │   └── openai [required: >=1.81.0,<2, installed: 1.84.0]
│   │   │       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│   │   │       │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│   │   │       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│   │   │       │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │       │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │       │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │       │   └── idna [required: Any, installed: 3.10]
│   │   │       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│   │   │       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│   │   │       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │       ├── sniffio [required: Any, installed: 1.3.1]
│   │   │       ├── tqdm [required: >4, installed: 4.67.1]
│   │   │       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
│   │   └── openai [required: >=1.14.0, installed: 1.84.0]
│   │       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│   │       │   ├── idna [required: >=2.8, installed: 3.10]
│   │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│   │       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│   │       │   ├── anyio [required: Any, installed: 4.9.0]
│   │       │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │       │   ├── certifi [required: Any, installed: 2025.4.26]
│   │       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │       │   └── idna [required: Any, installed: 3.10]
│   │       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│   │       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│   │       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │       ├── sniffio [required: Any, installed: 1.3.1]
│   │       ├── tqdm [required: >4, installed: 4.67.1]
│   │       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
│   ├── llama-index-core [required: >=0.12.0,<0.13, installed: 0.12.40]
│   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── wrapt [required: Any, installed: 1.17.2]
│   └── llama-index-llms-openai [required: >=0.4.0,<0.5, installed: 0.4.2]
│       ├── llama-index-core [required: >=0.12.37,<0.13, installed: 0.12.40]
│       │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│       │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│       │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│       │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│       │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│       │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│       │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│       │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│       │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│       │   │       ├── idna [required: >=2.0, installed: 3.10]
│       │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│       │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│       │   ├── aiosqlite [required: Any, installed: 0.21.0]
│       │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│       │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│       │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│       │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   ├── griffe [required: Any, installed: 1.7.3]
│       │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│       │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│       │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│       │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│       │   │   └── pydantic [required: Any, installed: 2.11.5]
│       │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│       │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│       │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│       │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│       │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│       │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│       │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│       │   ├── httpx [required: Any, installed: 0.28.1]
│       │   │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   │   └── idna [required: Any, installed: 3.10]
│       │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│       │   ├── networkx [required: >=3.0, installed: 3.5]
│       │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│       │   │   ├── click [required: Any, installed: 8.2.1]
│       │   │   ├── joblib [required: Any, installed: 1.5.1]
│       │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│       │   │   └── tqdm [required: Any, installed: 4.67.1]
│       │   ├── numpy [required: Any, installed: 2.2.6]
│       │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│       │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│       │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│       │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│       │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│       │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│       │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│       │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│       │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│       │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│       │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│       │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│       │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│       │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│       │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   └── wrapt [required: Any, installed: 1.17.2]
│       └── openai [required: >=1.81.0,<2, installed: 1.84.0]
│           ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│           │   ├── idna [required: >=2.8, installed: 3.10]
│           │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│           │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│           ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│           ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│           │   ├── anyio [required: Any, installed: 4.9.0]
│           │   │   ├── idna [required: >=2.8, installed: 3.10]
│           │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│           │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│           │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│           │   │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│           │   └── idna [required: Any, installed: 3.10]
│           ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│           ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│           │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│           ├── sniffio [required: Any, installed: 1.3.1]
│           ├── tqdm [required: >4, installed: 4.67.1]
│           └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
├── llama-index-question-gen-openai [required: >=0.3.0,<0.4, installed: 0.3.1]
│   ├── llama-index-core [required: >=0.12.0,<0.13, installed: 0.12.40]
│   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── wrapt [required: Any, installed: 1.17.2]
│   ├── llama-index-llms-openai [required: >=0.4.0,<0.5, installed: 0.4.2]
│   │   ├── llama-index-core [required: >=0.12.37,<0.13, installed: 0.12.40]
│   │   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   │   └── idna [required: Any, installed: 3.10]
│   │   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   │   └── wrapt [required: Any, installed: 1.17.2]
│   │   └── openai [required: >=1.81.0,<2, installed: 1.84.0]
│   │       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│   │       │   ├── idna [required: >=2.8, installed: 3.10]
│   │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│   │       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│   │       │   ├── anyio [required: Any, installed: 4.9.0]
│   │       │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │       │   ├── certifi [required: Any, installed: 2025.4.26]
│   │       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │       │   └── idna [required: Any, installed: 3.10]
│   │       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│   │       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│   │       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │       ├── sniffio [required: Any, installed: 1.3.1]
│   │       ├── tqdm [required: >4, installed: 4.67.1]
│   │       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
│   └── llama-index-program-openai [required: >=0.3.0,<0.4, installed: 0.3.2]
│       ├── llama-index-agent-openai [required: >=0.4.0,<0.5, installed: 0.4.9]
│       │   ├── llama-index-core [required: >=0.12.18,<0.13, installed: 0.12.40]
│       │   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│       │   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│       │   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│       │   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│       │   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│       │   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│       │   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│       │   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│       │   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│       │   │   │       ├── idna [required: >=2.0, installed: 3.10]
│       │   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│       │   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│       │   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│       │   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│       │   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│       │   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│       │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   │   ├── griffe [required: Any, installed: 1.7.3]
│       │   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│       │   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│       │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│       │   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│       │   │   │   └── pydantic [required: Any, installed: 2.11.5]
│       │   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│       │   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│       │   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│       │   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│       │   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│       │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│       │   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│       │   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│       │   │   ├── httpx [required: Any, installed: 0.28.1]
│       │   │   │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   │   │   └── idna [required: Any, installed: 3.10]
│       │   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│       │   │   ├── networkx [required: >=3.0, installed: 3.5]
│       │   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│       │   │   │   ├── click [required: Any, installed: 8.2.1]
│       │   │   │   ├── joblib [required: Any, installed: 1.5.1]
│       │   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│       │   │   │   └── tqdm [required: Any, installed: 4.67.1]
│       │   │   ├── numpy [required: Any, installed: 2.2.6]
│       │   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│       │   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│       │   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│       │   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│       │   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│       │   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│       │   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│       │   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│       │   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│       │   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│       │   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│       │   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│       │   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│       │   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│       │   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   │   └── wrapt [required: Any, installed: 1.17.2]
│       │   ├── llama-index-llms-openai [required: >=0.4.0,<0.5, installed: 0.4.2]
│       │   │   ├── llama-index-core [required: >=0.12.37,<0.13, installed: 0.12.40]
│       │   │   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│       │   │   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│       │   │   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│       │   │   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│       │   │   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│       │   │   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│       │   │   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│       │   │   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│       │   │   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│       │   │   │   │       ├── idna [required: >=2.0, installed: 3.10]
│       │   │   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│       │   │   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│       │   │   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│       │   │   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│       │   │   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│       │   │   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│       │   │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   │   │   ├── griffe [required: Any, installed: 1.7.3]
│       │   │   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│       │   │   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│       │   │   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│       │   │   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│       │   │   │   │   └── pydantic [required: Any, installed: 2.11.5]
│       │   │   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   │   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│       │   │   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│       │   │   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│       │   │   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│       │   │   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   │   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│       │   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│       │   │   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│       │   │   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│       │   │   │   ├── httpx [required: Any, installed: 0.28.1]
│       │   │   │   │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   │   │   │   └── idna [required: Any, installed: 3.10]
│       │   │   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│       │   │   │   ├── networkx [required: >=3.0, installed: 3.5]
│       │   │   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│       │   │   │   │   ├── click [required: Any, installed: 8.2.1]
│       │   │   │   │   ├── joblib [required: Any, installed: 1.5.1]
│       │   │   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│       │   │   │   │   └── tqdm [required: Any, installed: 4.67.1]
│       │   │   │   ├── numpy [required: Any, installed: 2.2.6]
│       │   │   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│       │   │   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│       │   │   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   │   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│       │   │   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│       │   │   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   │   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│       │   │   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│       │   │   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│       │   │   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│       │   │   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│       │   │   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│       │   │   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│       │   │   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   │   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│       │   │   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│       │   │   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│       │   │   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   │   │   └── wrapt [required: Any, installed: 1.17.2]
│       │   │   └── openai [required: >=1.81.0,<2, installed: 1.84.0]
│       │   │       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│       │   │       │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   │       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│       │   │       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│       │   │       │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │       │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   │       │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   │       │   └── idna [required: Any, installed: 3.10]
│       │   │       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│       │   │       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│       │   │       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   │       ├── sniffio [required: Any, installed: 1.3.1]
│       │   │       ├── tqdm [required: >4, installed: 4.67.1]
│       │   │       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
│       │   └── openai [required: >=1.14.0, installed: 1.84.0]
│       │       ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│       │       │   ├── idna [required: >=2.8, installed: 3.10]
│       │       │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │       │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │       ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│       │       ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│       │       │   ├── anyio [required: Any, installed: 4.9.0]
│       │       │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │       │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │       │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │       │   ├── certifi [required: Any, installed: 2025.4.26]
│       │       │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │       │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │       │   └── idna [required: Any, installed: 3.10]
│       │       ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│       │       ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│       │       │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │       │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │       │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │       │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │       │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │       │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │       ├── sniffio [required: Any, installed: 1.3.1]
│       │       ├── tqdm [required: >4, installed: 4.67.1]
│       │       └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
│       ├── llama-index-core [required: >=0.12.0,<0.13, installed: 0.12.40]
│       │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│       │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│       │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│       │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│       │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│       │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│       │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│       │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│       │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│       │   │       ├── idna [required: >=2.0, installed: 3.10]
│       │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│       │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│       │   ├── aiosqlite [required: Any, installed: 0.21.0]
│       │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│       │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│       │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│       │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   │   ├── griffe [required: Any, installed: 1.7.3]
│       │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│       │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│       │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│       │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│       │   │   └── pydantic [required: Any, installed: 2.11.5]
│       │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│       │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│       │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│       │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│       │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│       │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│       │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│       │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│       │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│       │   ├── httpx [required: Any, installed: 0.28.1]
│       │   │   ├── anyio [required: Any, installed: 4.9.0]
│       │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│       │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│       │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│       │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│       │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│       │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│       │   │   └── idna [required: Any, installed: 3.10]
│       │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│       │   ├── networkx [required: >=3.0, installed: 3.5]
│       │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│       │   │   ├── click [required: Any, installed: 8.2.1]
│       │   │   ├── joblib [required: Any, installed: 1.5.1]
│       │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│       │   │   └── tqdm [required: Any, installed: 4.67.1]
│       │   ├── numpy [required: Any, installed: 2.2.6]
│       │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│       │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│       │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│       │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│       │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│       │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│       │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│       │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│       │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│       │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│       │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│       │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│       │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│       │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│       │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│       │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│       │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│       │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│       │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│       │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│       │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│       │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│       │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│       │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│       │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│       │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│       │   └── wrapt [required: Any, installed: 1.17.2]
│       └── llama-index-llms-openai [required: >=0.4.0,<0.5, installed: 0.4.2]
│           ├── llama-index-core [required: >=0.12.37,<0.13, installed: 0.12.40]
│           │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│           │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│           │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│           │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│           │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│           │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│           │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│           │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│           │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│           │   │       ├── idna [required: >=2.0, installed: 3.10]
│           │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│           │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│           │   ├── aiosqlite [required: Any, installed: 0.21.0]
│           │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│           │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│           │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│           │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│           │   │   ├── griffe [required: Any, installed: 1.7.3]
│           │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│           │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│           │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│           │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│           │   │   └── pydantic [required: Any, installed: 2.11.5]
│           │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│           │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│           │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│           │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│           │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│           │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│           │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│           │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│           │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│           │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│           │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│           │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│           │   ├── httpx [required: Any, installed: 0.28.1]
│           │   │   ├── anyio [required: Any, installed: 4.9.0]
│           │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│           │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│           │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│           │   │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│           │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│           │   │   └── idna [required: Any, installed: 3.10]
│           │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│           │   ├── networkx [required: >=3.0, installed: 3.5]
│           │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│           │   │   ├── click [required: Any, installed: 8.2.1]
│           │   │   ├── joblib [required: Any, installed: 1.5.1]
│           │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│           │   │   └── tqdm [required: Any, installed: 4.67.1]
│           │   ├── numpy [required: Any, installed: 2.2.6]
│           │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│           │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│           │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│           │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│           │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│           │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│           │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│           │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│           │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│           │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│           │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│           │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│           │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│           │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│           │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│           │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│           │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│           │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│           │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│           │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│           │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│           │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│           │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│           │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│           │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│           │   └── wrapt [required: Any, installed: 1.17.2]
│           └── openai [required: >=1.81.0,<2, installed: 1.84.0]
│               ├── anyio [required: >=3.5.0,<5, installed: 4.9.0]
│               │   ├── idna [required: >=2.8, installed: 3.10]
│               │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│               │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│               ├── distro [required: >=1.7.0,<2, installed: 1.9.0]
│               ├── httpx [required: >=0.23.0,<1, installed: 0.28.1]
│               │   ├── anyio [required: Any, installed: 4.9.0]
│               │   │   ├── idna [required: >=2.8, installed: 3.10]
│               │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│               │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│               │   ├── certifi [required: Any, installed: 2025.4.26]
│               │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│               │   │   ├── certifi [required: Any, installed: 2025.4.26]
│               │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│               │   └── idna [required: Any, installed: 3.10]
│               ├── jiter [required: >=0.4.0,<1, installed: 0.10.0]
│               ├── pydantic [required: >=1.9.0,<3, installed: 2.11.5]
│               │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│               │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│               │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│               │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│               │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│               │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│               ├── sniffio [required: Any, installed: 1.3.1]
│               ├── tqdm [required: >4, installed: 4.67.1]
│               └── typing_extensions [required: >=4.11,<5, installed: 4.14.0]
├── llama-index-readers-file [required: >=0.4.0,<0.5, installed: 0.4.8]
│   ├── beautifulsoup4 [required: >=4.12.3,<5, installed: 4.13.4]
│   │   ├── soupsieve [required: >1.2, installed: 2.7]
│   │   └── typing_extensions [required: >=4.0.0, installed: 4.14.0]
│   ├── llama-index-core [required: >=0.12.0,<0.13, installed: 0.12.40]
│   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── wrapt [required: Any, installed: 1.17.2]
│   ├── pandas [required: Any, installed: 2.2.3]
│   │   ├── numpy [required: >=1.26.0, installed: 2.2.6]
│   │   ├── python-dateutil [required: >=2.8.2, installed: 2.9.0.post0]
│   │   │   └── six [required: >=1.5, installed: 1.17.0]
│   │   ├── pytz [required: >=2020.1, installed: 2025.2]
│   │   └── tzdata [required: >=2022.7, installed: 2025.2]
│   ├── pypdf [required: >=5.1.0,<6, installed: 5.6.0]
│   └── striprtf [required: >=0.0.26,<0.0.27, installed: 0.0.26]
├── llama-index-readers-llama-parse [required: >=0.4.0, installed: 0.4.0]
│   ├── llama-index-core [required: >=0.12.0,<0.13.0, installed: 0.12.40]
│   │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│   │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│   │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│   │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│   │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│   │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│   │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│   │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│   │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│   │   │       ├── idna [required: >=2.0, installed: 3.10]
│   │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│   │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│   │   ├── aiosqlite [required: Any, installed: 0.21.0]
│   │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│   │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│   │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│   │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   │   ├── griffe [required: Any, installed: 1.7.3]
│   │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│   │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│   │   │   └── pydantic [required: Any, installed: 2.11.5]
│   │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│   │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│   │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│   │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│   │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│   │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│   │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── httpx [required: Any, installed: 0.28.1]
│   │   │   ├── anyio [required: Any, installed: 4.9.0]
│   │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│   │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│   │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│   │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│   │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│   │   │   └── idna [required: Any, installed: 3.10]
│   │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│   │   ├── networkx [required: >=3.0, installed: 3.5]
│   │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│   │   │   ├── click [required: Any, installed: 8.2.1]
│   │   │   ├── joblib [required: Any, installed: 1.5.1]
│   │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│   │   │   └── tqdm [required: Any, installed: 4.67.1]
│   │   ├── numpy [required: Any, installed: 2.2.6]
│   │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│   │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│   │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│   │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│   │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│   │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│   │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│   │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│   │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│   │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│   │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│   │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│   │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│   │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│   │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│   │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│   │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│   │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│   │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│   │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│   │   └── wrapt [required: Any, installed: 1.17.2]
│   └── llama-parse [required: >=0.5.0, installed: 0.6.28]
│       └── llama-cloud-services [required: >=0.6.28, installed: 0.6.28]
│           ├── click [required: >=8.1.7,<9.0.0, installed: 8.2.1]
│           ├── llama-cloud [required: ==0.1.23, installed: 0.1.23]
│           │   ├── certifi [required: >=2024.7.4, installed: 2025.4.26]
│           │   ├── httpx [required: >=0.20.0, installed: 0.28.1]
│           │   │   ├── anyio [required: Any, installed: 4.9.0]
│           │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│           │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│           │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│           │   │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│           │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│           │   │   └── idna [required: Any, installed: 3.10]
│           │   └── pydantic [required: >=1.10, installed: 2.11.5]
│           │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│           ├── llama-index-core [required: >=0.12.0, installed: 0.12.40]
│           │   ├── aiohttp [required: >=3.8.6,<4, installed: 3.12.7]
│           │   │   ├── aiohappyeyeballs [required: >=2.5.0, installed: 2.6.1]
│           │   │   ├── aiosignal [required: >=1.1.2, installed: 1.3.2]
│           │   │   │   └── frozenlist [required: >=1.1.0, installed: 1.6.0]
│           │   │   ├── attrs [required: >=17.3.0, installed: 25.3.0]
│           │   │   ├── frozenlist [required: >=1.1.1, installed: 1.6.0]
│           │   │   ├── multidict [required: >=4.5,<7.0, installed: 6.4.4]
│           │   │   ├── propcache [required: >=0.2.0, installed: 0.3.1]
│           │   │   └── yarl [required: >=1.17.0,<2.0, installed: 1.20.0]
│           │   │       ├── idna [required: >=2.0, installed: 3.10]
│           │   │       ├── multidict [required: >=4.0, installed: 6.4.4]
│           │   │       └── propcache [required: >=0.2.1, installed: 0.3.1]
│           │   ├── aiosqlite [required: Any, installed: 0.21.0]
│           │   │   └── typing_extensions [required: >=4.0, installed: 4.14.0]
│           │   ├── banks [required: >=2.0.0,<3, installed: 2.1.2]
│           │   │   ├── Deprecated [required: Any, installed: 1.2.18]
│           │   │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│           │   │   ├── griffe [required: Any, installed: 1.7.3]
│           │   │   │   └── colorama [required: >=0.4, installed: 0.4.6]
│           │   │   ├── Jinja2 [required: Any, installed: 3.1.6]
│           │   │   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│           │   │   ├── platformdirs [required: Any, installed: 4.3.8]
│           │   │   └── pydantic [required: Any, installed: 2.11.5]
│           │   │       ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           │   │       ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │   │       │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           │   │       ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           │   │       └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           │   │           └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│           │   ├── dataclasses-json [required: Any, installed: 0.6.7]
│           │   │   ├── marshmallow [required: >=3.18.0,<4.0.0, installed: 3.26.1]
│           │   │   │   └── packaging [required: >=17.0, installed: 24.2]
│           │   │   └── typing-inspect [required: >=0.4.0,<1, installed: 0.9.0]
│           │   │       ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│           │   │       └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│           │   ├── Deprecated [required: >=1.2.9.3, installed: 1.2.18]
│           │   │   └── wrapt [required: >=1.10,<2, installed: 1.17.2]
│           │   ├── dirtyjson [required: >=1.0.8,<2, installed: 1.0.8]
│           │   ├── filetype [required: >=1.2.0,<2, installed: 1.2.0]
│           │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│           │   ├── httpx [required: Any, installed: 0.28.1]
│           │   │   ├── anyio [required: Any, installed: 4.9.0]
│           │   │   │   ├── idna [required: >=2.8, installed: 3.10]
│           │   │   │   ├── sniffio [required: >=1.1, installed: 1.3.1]
│           │   │   │   └── typing_extensions [required: >=4.5, installed: 4.14.0]
│           │   │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   │   ├── httpcore [required: ==1.*, installed: 1.0.9]
│           │   │   │   ├── certifi [required: Any, installed: 2025.4.26]
│           │   │   │   └── h11 [required: >=0.16, installed: 0.16.0]
│           │   │   └── idna [required: Any, installed: 3.10]
│           │   ├── nest-asyncio [required: >=1.5.8,<2, installed: 1.6.0]
│           │   ├── networkx [required: >=3.0, installed: 3.5]
│           │   ├── nltk [required: >3.8.1, installed: 3.9.1]
│           │   │   ├── click [required: Any, installed: 8.2.1]
│           │   │   ├── joblib [required: Any, installed: 1.5.1]
│           │   │   ├── regex [required: >=2021.8.3, installed: 2024.11.6]
│           │   │   └── tqdm [required: Any, installed: 4.67.1]
│           │   ├── numpy [required: Any, installed: 2.2.6]
│           │   ├── pillow [required: >=9.0.0, installed: 11.2.1]
│           │   ├── pydantic [required: >=2.8.0, installed: 2.11.5]
│           │   │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           │   │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │   │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           │   │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           │   │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           │   │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│           │   ├── PyYAML [required: >=6.0.1, installed: 6.0.2]
│           │   ├── requests [required: >=2.31.0, installed: 2.32.3]
│           │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│           │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│           │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│           │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│           │   ├── SQLAlchemy [required: >=1.4.49, installed: 2.0.41]
│           │   │   ├── greenlet [required: >=1, installed: 3.2.2]
│           │   │   └── typing_extensions [required: >=4.6.0, installed: 4.14.0]
│           │   ├── tenacity [required: >=8.2.0,<10.0.0,!=8.4.0, installed: 9.1.2]
│           │   ├── tiktoken [required: >=0.7.0, installed: 0.9.0]
│           │   │   ├── regex [required: >=2022.1.18, installed: 2024.11.6]
│           │   │   └── requests [required: >=2.26.0, installed: 2.32.3]
│           │   │       ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│           │   │       ├── idna [required: >=2.5,<4, installed: 3.10]
│           │   │       ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│           │   │       └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│           │   ├── tqdm [required: >=4.66.1,<5, installed: 4.67.1]
│           │   ├── typing_extensions [required: >=4.5.0, installed: 4.14.0]
│           │   ├── typing-inspect [required: >=0.8.0, installed: 0.9.0]
│           │   │   ├── mypy_extensions [required: >=0.3.0, installed: 1.1.0]
│           │   │   └── typing_extensions [required: >=3.7.4, installed: 4.14.0]
│           │   └── wrapt [required: Any, installed: 1.17.2]
│           ├── platformdirs [required: >=4.3.7,<5.0.0, installed: 4.3.8]
│           ├── pydantic [required: >=2.8,!=2.10, installed: 2.11.5]
│           │   ├── annotated-types [required: >=0.6.0, installed: 0.7.0]
│           │   ├── pydantic_core [required: ==2.33.2, installed: 2.33.2]
│           │   │   └── typing_extensions [required: >=4.6.0,!=4.7.0, installed: 4.14.0]
│           │   ├── typing_extensions [required: >=4.12.2, installed: 4.14.0]
│           │   └── typing-inspection [required: >=0.4.0, installed: 0.4.1]
│           │       └── typing_extensions [required: >=4.12.0, installed: 4.14.0]
│           └── python-dotenv [required: >=1.0.1,<2.0.0, installed: 1.1.0]
└── nltk [required: >3.8.1, installed: 3.9.1]
    ├── click [required: Any, installed: 8.2.1]
    ├── joblib [required: Any, installed: 1.5.1]
    ├── regex [required: >=2021.8.3, installed: 2024.11.6]
    └── tqdm [required: Any, installed: 4.67.1]
pipdeptree==2.26.1
├── packaging [required: >=24.1, installed: 24.2]
└── pip [required: >=24.2, installed: 25.0.1]
sentence-transformers==4.1.0
├── transformers [required: >=4.41.0,<5.0.0, installed: 4.52.4]
│   ├── filelock [required: Any, installed: 3.18.0]
│   ├── huggingface-hub [required: >=0.30.0,<1.0, installed: 0.32.4]
│   │   ├── filelock [required: Any, installed: 3.18.0]
│   │   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │   ├── packaging [required: >=20.9, installed: 24.2]
│   │   ├── PyYAML [required: >=5.1, installed: 6.0.2]
│   │   ├── requests [required: Any, installed: 2.32.3]
│   │   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │   ├── tqdm [required: >=4.42.1, installed: 4.67.1]
│   │   ├── typing_extensions [required: >=*******, installed: 4.14.0]
│   │   └── hf-xet [required: >=1.1.2,<2.0.0, installed: 1.1.2]
│   ├── numpy [required: >=1.17, installed: 2.2.6]
│   ├── packaging [required: >=20.0, installed: 24.2]
│   ├── PyYAML [required: >=5.1, installed: 6.0.2]
│   ├── regex [required: !=2019.12.17, installed: 2024.11.6]
│   ├── requests [required: Any, installed: 2.32.3]
│   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   ├── tokenizers [required: >=0.21,<0.22, installed: 0.21.1]
│   │   └── huggingface-hub [required: >=0.16.4,<1.0, installed: 0.32.4]
│   │       ├── filelock [required: Any, installed: 3.18.0]
│   │       ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   │       ├── packaging [required: >=20.9, installed: 24.2]
│   │       ├── PyYAML [required: >=5.1, installed: 6.0.2]
│   │       ├── requests [required: Any, installed: 2.32.3]
│   │       │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │       │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │       │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │       │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   │       ├── tqdm [required: >=4.42.1, installed: 4.67.1]
│   │       ├── typing_extensions [required: >=*******, installed: 4.14.0]
│   │       └── hf-xet [required: >=1.1.2,<2.0.0, installed: 1.1.2]
│   ├── safetensors [required: >=0.4.3, installed: 0.5.3]
│   └── tqdm [required: >=4.27, installed: 4.67.1]
├── tqdm [required: Any, installed: 4.67.1]
├── torch [required: >=1.11.0, installed: 2.7.0]
│   ├── filelock [required: Any, installed: 3.18.0]
│   ├── typing_extensions [required: >=4.10.0, installed: 4.14.0]
│   ├── setuptools [required: Any, installed: 75.6.0]
│   ├── sympy [required: >=1.13.3, installed: 1.14.0]
│   │   └── mpmath [required: >=1.1.0,<1.4, installed: 1.3.0]
│   ├── networkx [required: Any, installed: 3.5]
│   ├── Jinja2 [required: Any, installed: 3.1.6]
│   │   └── MarkupSafe [required: >=2.0, installed: 3.0.2]
│   ├── fsspec [required: Any, installed: 2025.5.1]
│   ├── nvidia-cuda-nvrtc-cu12 [required: ==12.6.77, installed: 12.6.77]
│   ├── nvidia-cuda-runtime-cu12 [required: ==12.6.77, installed: 12.6.77]
│   ├── nvidia-cuda-cupti-cu12 [required: ==12.6.80, installed: 12.6.80]
│   ├── nvidia-cudnn-cu12 [required: ==9.5.1.17, installed: 9.5.1.17]
│   │   └── nvidia-cublas-cu12 [required: Any, installed: 12.6.4.1]
│   ├── nvidia-cublas-cu12 [required: ==12.6.4.1, installed: 12.6.4.1]
│   ├── nvidia-cufft-cu12 [required: ==11.3.0.4, installed: 11.3.0.4]
│   │   └── nvidia-nvjitlink-cu12 [required: Any, installed: 12.6.85]
│   ├── nvidia-curand-cu12 [required: ==10.3.7.77, installed: 10.3.7.77]
│   ├── nvidia-cusolver-cu12 [required: ==11.7.1.2, installed: 11.7.1.2]
│   │   ├── nvidia-cublas-cu12 [required: Any, installed: 12.6.4.1]
│   │   ├── nvidia-nvjitlink-cu12 [required: Any, installed: 12.6.85]
│   │   └── nvidia-cusparse-cu12 [required: Any, installed: 12.5.4.2]
│   │       └── nvidia-nvjitlink-cu12 [required: Any, installed: 12.6.85]
│   ├── nvidia-cusparse-cu12 [required: ==12.5.4.2, installed: 12.5.4.2]
│   │   └── nvidia-nvjitlink-cu12 [required: Any, installed: 12.6.85]
│   ├── nvidia-cusparselt-cu12 [required: ==0.6.3, installed: 0.6.3]
│   ├── nvidia-nccl-cu12 [required: ==2.26.2, installed: 2.26.2]
│   ├── nvidia-nvtx-cu12 [required: ==12.6.77, installed: 12.6.77]
│   ├── nvidia-nvjitlink-cu12 [required: ==12.6.85, installed: 12.6.85]
│   ├── nvidia-cufile-cu12 [required: ==********, installed: ********]
│   └── triton [required: ==3.3.0, installed: 3.3.0]
│       └── setuptools [required: >=40.8.0, installed: 75.6.0]
├── scikit-learn [required: Any, installed: 1.6.1]
│   ├── numpy [required: >=1.19.5, installed: 2.2.6]
│   ├── scipy [required: >=1.6.0, installed: 1.15.3]
│   │   └── numpy [required: >=1.23.5,<2.5, installed: 2.2.6]
│   ├── joblib [required: >=1.2.0, installed: 1.5.1]
│   └── threadpoolctl [required: >=3.1.0, installed: 3.6.0]
├── scipy [required: Any, installed: 1.15.3]
│   └── numpy [required: >=1.23.5,<2.5, installed: 2.2.6]
├── huggingface-hub [required: >=0.20.0, installed: 0.32.4]
│   ├── filelock [required: Any, installed: 3.18.0]
│   ├── fsspec [required: >=2023.5.0, installed: 2025.5.1]
│   ├── packaging [required: >=20.9, installed: 24.2]
│   ├── PyYAML [required: >=5.1, installed: 6.0.2]
│   ├── requests [required: Any, installed: 2.32.3]
│   │   ├── charset-normalizer [required: >=2,<4, installed: 3.4.2]
│   │   ├── idna [required: >=2.5,<4, installed: 3.10]
│   │   ├── urllib3 [required: >=1.21.1,<3, installed: 2.4.0]
│   │   └── certifi [required: >=2017.4.17, installed: 2025.4.26]
│   ├── tqdm [required: >=4.42.1, installed: 4.67.1]
│   ├── typing_extensions [required: >=*******, installed: 4.14.0]
│   └── hf-xet [required: >=1.1.2,<2.0.0, installed: 1.1.2]
├── pillow [required: Any, installed: 11.2.1]
└── typing_extensions [required: >=4.5.0, installed: 4.14.0]
tinycss2==1.4.0
└── webencodings [required: >=0.4, installed: 0.5.1]
uri-template==1.3.0
webcolors==24.11.1
