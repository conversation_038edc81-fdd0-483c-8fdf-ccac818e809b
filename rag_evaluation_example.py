"""
RAG (Retrieval-Augmented Generation) Evaluation Example using Phoenix.
This example demonstrates how to evaluate RAG systems with Phoenix.
"""

import os
import pandas as pd
import nest_asyncio
from phoenix.evals import (
    RelevanceEvaluator,
    QAEvaluator, 
    HallucinationEvaluator,
    OpenAIModel, 
    run_evals
)
from dotenv import load_dotenv

# Enable nested async
nest_asyncio.apply()
load_dotenv()

def create_rag_dataset():
    """
    Create a sample RAG dataset with queries, retrieved documents, and generated responses.
    """
    data = [
        {
            "query": "What is machine learning?",
            "retrieved_documents": [
                "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.",
                "Machine learning algorithms build mathematical models based on training data to make predictions or decisions.",
                "Common types of machine learning include supervised learning, unsupervised learning, and reinforcement learning."
            ],
            "response": "Machine learning is a subset of artificial intelligence that allows computers to learn and improve from experience without explicit programming. It uses algorithms to build mathematical models from training data to make predictions or decisions.",
            "reference": "Machine learning is a method of data analysis that automates analytical model building using algorithms that iteratively learn from data.",
            "context": "Machine learning is a subset of AI that enables systems to automatically learn and improve from experience."
        },
        {
            "query": "How does photosynthesis work?",
            "retrieved_documents": [
                "Photosynthesis is the process by which plants convert light energy into chemical energy stored in glucose.",
                "During photosynthesis, plants use chlorophyll to capture sunlight and convert carbon dioxide and water into glucose and oxygen.",
                "The process occurs in two main stages: light-dependent reactions and light-independent reactions (Calvin cycle)."
            ],
            "response": "Photosynthesis is the process where plants convert sunlight into chemical energy. Plants use chlorophyll to capture light and convert CO2 and water into glucose and oxygen through light-dependent and light-independent reactions.",
            "reference": "Photosynthesis converts light energy to chemical energy, producing glucose and oxygen from CO2 and water.",
            "context": "Photosynthesis is a biological process that converts light energy into chemical energy in plants."
        },
        {
            "query": "What is the capital of Australia?",
            "retrieved_documents": [
                "Australia is a country and continent surrounded by the Indian and Pacific oceans.",
                "Sydney is the largest city in Australia and a major financial center.",
                "Melbourne is known for its cultural diversity and is often considered the cultural capital of Australia."
            ],
            "response": "Based on the information provided, Sydney appears to be the most important city in Australia as it's the largest city and a major financial center.",
            "reference": "Canberra is the capital city of Australia.",
            "context": "Canberra is the capital of Australia, though Sydney and Melbourne are larger cities."
        },
        {
            "query": "Explain quantum computing",
            "retrieved_documents": [
                "Quantum computing uses quantum mechanical phenomena like superposition and entanglement to process information.",
                "Unlike classical bits that are either 0 or 1, quantum bits (qubits) can exist in multiple states simultaneously.",
                "Quantum computers have the potential to solve certain problems exponentially faster than classical computers."
            ],
            "response": "Quantum computing leverages quantum mechanical principles such as superposition and entanglement to process information. Unlike classical bits, qubits can exist in multiple states simultaneously, potentially allowing quantum computers to solve certain problems much faster than traditional computers.",
            "reference": "Quantum computing uses quantum mechanics to process information using qubits instead of classical bits.",
            "context": "Quantum computing is a type of computation that harnesses quantum mechanical phenomena."
        },
        {
            "query": "What are the health benefits of exercise?",
            "retrieved_documents": [
                "Regular exercise can help prevent chronic diseases like heart disease and diabetes.",
                "Exercise improves mental health by reducing symptoms of depression and anxiety.",
                "Physical activity strengthens bones and muscles while improving cardiovascular health."
            ],
            "response": "Exercise provides numerous health benefits including preventing chronic diseases, improving mental health, and strengthening the cardiovascular system. It also helps build stronger bones and muscles while reducing depression and anxiety.",
            "reference": "Exercise improves physical and mental health, prevents disease, and strengthens the body.",
            "context": "Regular physical activity has wide-ranging health benefits for both body and mind."
        }
    ]
    
    return pd.DataFrame(data)

def setup_rag_evaluators():
    """
    Set up evaluators specifically for RAG evaluation.
    """
    if not os.getenv("OPENAI_API_KEY"):
        raise ValueError("Please set OPENAI_API_KEY environment variable")
    
    eval_model = OpenAIModel(model="gpt-4o", temperature=0.0)
    
    evaluators = {
        "relevance": RelevanceEvaluator(eval_model),
        "hallucination": HallucinationEvaluator(eval_model),
        "qa_correctness": QAEvaluator(eval_model)
    }
    
    return evaluators

def prepare_rag_dataframe(df):
    """
    Prepare the dataframe for RAG evaluation.
    Different evaluators expect different column formats.
    """
    eval_df = df.copy()
    
    # Rename columns for evaluators
    eval_df.rename(columns={
        "query": "input",
        "response": "output"
    }, inplace=True)
    
    # For relevance evaluation, we need to format retrieved documents
    # Convert list of documents to a single context string
    eval_df["context"] = eval_df["retrieved_documents"].apply(
        lambda docs: "\n\n".join([f"Document {i+1}: {doc}" for i, doc in enumerate(docs)])
    )
    
    return eval_df

def evaluate_retrieval_relevance(eval_df, evaluators):
    """
    Evaluate the relevance of retrieved documents to the query.
    """
    print("🔍 Evaluating retrieval relevance...")
    
    # For relevance evaluation, we evaluate each retrieved document separately
    relevance_results = []
    
    for idx, row in eval_df.iterrows():
        query = row["input"]
        documents = row["retrieved_documents"]
        
        for doc_idx, document in enumerate(documents):
            # Create a temporary dataframe for this document
            temp_df = pd.DataFrame([{
                "input": query,
                "output": document,  # Use document as output for relevance evaluation
                "context": document
            }])
            
            # Run relevance evaluation
            result = run_evals(
                dataframe=temp_df,
                evaluators=[evaluators["relevance"]],
                provide_explanation=True
            )[0]
            
            relevance_results.append({
                "query_idx": idx,
                "document_idx": doc_idx,
                "query": query,
                "document": document,
                "relevance_score": result["label"].iloc[0],
                "relevance_explanation": result["explanation"].iloc[0]
            })
    
    return pd.DataFrame(relevance_results)

def run_rag_evaluations():
    """
    Run comprehensive RAG evaluations.
    """
    print("🔧 Running RAG Evaluations with Phoenix")
    print("=" * 40)
    
    # Create RAG dataset
    print("📊 Creating RAG dataset...")
    df = create_rag_dataset()
    print(f"   Dataset size: {len(df)} examples")
    
    # Setup evaluators
    print("🤖 Setting up RAG evaluators...")
    evaluators = setup_rag_evaluators()
    
    # Prepare dataframe
    eval_df = prepare_rag_dataframe(df)
    
    # 1. Evaluate retrieval relevance
    relevance_results = evaluate_retrieval_relevance(eval_df, evaluators)
    
    # 2. Evaluate response quality (hallucination and Q&A correctness)
    print("⚡ Evaluating response hallucination...")
    hallucination_results = run_evals(
        dataframe=eval_df,
        evaluators=[evaluators["hallucination"]],
        provide_explanation=True
    )[0]
    
    print("⚡ Evaluating Q&A correctness...")
    qa_results = run_evals(
        dataframe=eval_df,
        evaluators=[evaluators["qa_correctness"]],
        provide_explanation=True
    )[0]
    
    return df, relevance_results, hallucination_results, qa_results

def analyze_rag_results(df, relevance_results, hallucination_results, qa_results):
    """
    Analyze and display RAG evaluation results.
    """
    print("\n📈 RAG Evaluation Results")
    print("=" * 30)
    
    # Combine results
    results_df = df.copy()
    results_df["hallucination_score"] = hallucination_results["label"]
    results_df["hallucination_explanation"] = hallucination_results["explanation"]
    results_df["qa_score"] = qa_results["label"]
    results_df["qa_explanation"] = qa_results["explanation"]
    
    # Calculate retrieval metrics
    print("📊 Retrieval Relevance Summary:")
    total_docs = len(relevance_results)
    relevant_docs = len(relevance_results[relevance_results["relevance_score"] == "relevant"])
    print(f"   Total retrieved documents: {total_docs}")
    print(f"   Relevant documents: {relevant_docs}")
    print(f"   Relevance rate: {relevant_docs/total_docs:.2%}")
    
    # Response quality metrics
    print(f"\n📊 Response Quality Summary:")
    print(f"   Total responses: {len(results_df)}")
    print(f"   Factual responses: {sum(results_df['hallucination_score'] == 'factual')}")
    print(f"   Correct Q&A responses: {sum(results_df['qa_score'] == 'correct')}")
    print(f"   Hallucination rate: {sum(results_df['hallucination_score'] == 'hallucinated')/len(results_df):.2%}")
    print(f"   Q&A accuracy: {sum(results_df['qa_score'] == 'correct')/len(results_df):.2%}")
    
    # Detailed results by query
    print(f"\n📋 Detailed Results by Query:")
    for idx, row in results_df.iterrows():
        print(f"\n--- Query {idx + 1} ---")
        print(f"Query: {row['query']}")
        print(f"Response: {row['response'][:100]}{'...' if len(row['response']) > 100 else ''}")
        
        # Show retrieval relevance for this query
        query_relevance = relevance_results[relevance_results["query_idx"] == idx]
        relevant_count = len(query_relevance[query_relevance["relevance_score"] == "relevant"])
        print(f"Retrieved docs relevance: {relevant_count}/{len(query_relevance)} relevant")
        
        print(f"Hallucination: {row['hallucination_score']} - {row['hallucination_explanation']}")
        print(f"Q&A Correctness: {row['qa_score']} - {row['qa_explanation']}")
    
    return results_df, relevance_results

def calculate_rag_metrics(results_df, relevance_results):
    """
    Calculate comprehensive RAG metrics.
    """
    print("\n📊 RAG Performance Metrics")
    print("=" * 30)
    
    # Retrieval metrics
    total_docs = len(relevance_results)
    relevant_docs = len(relevance_results[relevance_results["relevance_score"] == "relevant"])
    
    # Response metrics
    factual_responses = sum(results_df["hallucination_score"] == "factual")
    correct_responses = sum(results_df["qa_score"] == "correct")
    total_responses = len(results_df)
    
    metrics = {
        "retrieval_precision": relevant_docs / total_docs if total_docs > 0 else 0,
        "response_factuality": factual_responses / total_responses,
        "response_correctness": correct_responses / total_responses,
        "overall_rag_score": (relevant_docs / total_docs + factual_responses / total_responses + correct_responses / total_responses) / 3
    }
    
    print(f"Retrieval Precision: {metrics['retrieval_precision']:.2%}")
    print(f"Response Factuality: {metrics['response_factuality']:.2%}")
    print(f"Response Correctness: {metrics['response_correctness']:.2%}")
    print(f"Overall RAG Score: {metrics['overall_rag_score']:.2%}")
    
    return metrics

def main():
    """
    Main function to run the RAG evaluation example.
    """
    try:
        # Run RAG evaluations
        df, relevance_results, hallucination_results, qa_results = run_rag_evaluations()
        
        # Analyze results
        results_df, relevance_df = analyze_rag_results(df, relevance_results, hallucination_results, qa_results)
        
        # Calculate metrics
        metrics = calculate_rag_metrics(results_df, relevance_results)
        
        # Save results
        results_df.to_csv("rag_evaluation_results.csv", index=False)
        relevance_results.to_csv("rag_retrieval_relevance.csv", index=False)
        
        # Save metrics
        metrics_df = pd.DataFrame([metrics])
        metrics_df.to_csv("rag_metrics.csv", index=False)
        
        print(f"\n💾 Results saved:")
        print(f"   - rag_evaluation_results.csv")
        print(f"   - rag_retrieval_relevance.csv")
        print(f"   - rag_metrics.csv")
        
        print(f"\n✅ RAG evaluation example completed successfully!")
        print(f"🔗 View results in Phoenix UI or check the CSV files.")
        
    except Exception as e:
        print(f"❌ Error running RAG evaluations: {str(e)}")
        print("Make sure you have set your OPENAI_API_KEY environment variable.")

if __name__ == "__main__":
    main()
