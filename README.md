# Arize Phoenix LLM Evaluations - POC Examples

This repository contains a comprehensive set of examples for using Arize Phoenix to evaluate Large Language Model (LLM) applications. These examples are designed to help you understand the Phoenix framework and get started with LLM evaluations.

## 🚀 Quick Start

### Prerequisites

1. **Python 3.8+** installed on your system
2. **OpenAI API Key** - Sign up at [OpenAI](https://platform.openai.com/) and get your API key
3. **Git** (optional, for cloning)

### Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd llm-evals
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up your OpenAI API key**
   ```bash
   # Option 1: Environment variable
   export OPENAI_API_KEY="your-api-key-here"
   
   # Option 2: Create a .env file
   echo "OPENAI_API_KEY=your-api-key-here" > .env
   ```

### Running the Examples

1. **Start Phoenix** (run this first)
   ```bash
   python setup_phoenix.py
   ```
   This will launch the Phoenix UI at `http://localhost:6006`

2. **Run the evaluation examples** (in separate terminal windows)
   ```bash
   # Basic evaluations with pre-built evaluators
   python basic_evaluation_example.py
   
   # Custom evaluators for specific use cases
   python custom_evaluation_example.py
   
   # Tracing LLM calls and evaluating them
   python tracing_with_evaluation.py
   
   # RAG (Retrieval-Augmented Generation) evaluations
   python rag_evaluation_example.py
   ```

## 📁 File Overview

### Core Files

- **`setup_phoenix.py`** - Sets up and launches Phoenix server
- **`requirements.txt`** - Python dependencies

### Example Scripts

- **`basic_evaluation_example.py`** - Demonstrates basic evaluations using Phoenix's pre-built evaluators
- **`custom_evaluation_example.py`** - Shows how to create and use custom evaluators
- **`tracing_with_evaluation.py`** - Integrates LLM tracing with evaluation
- **`rag_evaluation_example.py`** - Comprehensive RAG system evaluation

## 🧠 What You'll Learn

### 1. Basic Evaluations (`basic_evaluation_example.py`)
- **Hallucination Detection** - Identify when LLMs generate false information
- **Q&A Correctness** - Evaluate answer quality against reference data
- **Toxicity Detection** - Check for harmful or inappropriate content
- **Batch Evaluation** - Process multiple examples efficiently

### 2. Custom Evaluators (`custom_evaluation_example.py`)
- **Code Quality Assessment** - Evaluate generated code for correctness and best practices
- **Sentiment Analysis** - Analyze emotional tone in responses
- **Factual Accuracy** - Check facts against provided context
- **Custom Evaluation Templates** - Build evaluators for your specific needs

### 3. Tracing + Evaluation (`tracing_with_evaluation.py`)
- **LLM Call Tracing** - Monitor and record LLM interactions
- **Automatic Evaluation** - Evaluate traced calls automatically
- **Phoenix UI Integration** - View traces and evaluations in the web interface
- **End-to-End Workflow** - Complete observability and evaluation pipeline

### 4. RAG Evaluations (`rag_evaluation_example.py`)
- **Retrieval Relevance** - Evaluate quality of retrieved documents
- **Context Utilization** - Check if responses properly use retrieved context
- **RAG-Specific Metrics** - Comprehensive metrics for RAG systems
- **Multi-Stage Evaluation** - Evaluate both retrieval and generation stages

## 🔧 Key Phoenix Features Demonstrated

### Pre-Built Evaluators
- **HallucinationEvaluator** - Detects factual inconsistencies
- **QAEvaluator** - Measures answer correctness
- **ToxicityEvaluator** - Identifies harmful content
- **RelevanceEvaluator** - Assesses document relevance

### Custom Evaluation Framework
- **BaseEvaluator** - Foundation for custom evaluators
- **ClassificationTemplate** - Template for classification tasks
- **Flexible Criteria** - Define your own evaluation criteria

### Observability & Tracing
- **OpenTelemetry Integration** - Industry-standard tracing
- **Automatic Instrumentation** - Trace LLM calls automatically
- **Web UI** - Visual interface for exploring traces and evaluations

### Data Management
- **Pandas Integration** - Work with familiar data structures
- **Export/Import** - Save and load evaluation results
- **Batch Processing** - Evaluate large datasets efficiently

## 📊 Understanding the Results

### Evaluation Scores
- **Binary Classifications** - `correct`/`incorrect`, `factual`/`hallucinated`, etc.
- **Explanations** - Detailed reasoning for each evaluation
- **Confidence Scores** - Numerical confidence in evaluations

### Metrics Calculated
- **Accuracy Rates** - Percentage of correct responses
- **Error Rates** - Frequency of different error types
- **Quality Scores** - Overall quality assessments

### Output Files
Each example generates CSV files with detailed results:
- `basic_evaluation_results.csv`
- `custom_evaluation_results.csv`
- `tracing_evaluation_results.csv`
- `rag_evaluation_results.csv`
- `rag_retrieval_relevance.csv`
- `rag_metrics.csv`

## 🎯 Next Steps

### Customize for Your Use Case
1. **Modify the datasets** - Replace sample data with your actual LLM outputs
2. **Adjust evaluation criteria** - Customize evaluators for your specific requirements
3. **Add new evaluators** - Create evaluators for domain-specific quality measures
4. **Integrate with your pipeline** - Add Phoenix evaluations to your existing workflows

### Advanced Features to Explore
- **Real-time Evaluation** - Evaluate LLM outputs in production
- **A/B Testing** - Compare different LLM configurations
- **Fine-tuning Feedback** - Use evaluation results to improve models
- **Multi-modal Evaluation** - Evaluate text, images, and other modalities

## 🔗 Useful Resources

- **Phoenix Documentation** - https://docs.arize.com/phoenix
- **Phoenix GitHub** - https://github.com/Arize-ai/phoenix
- **Community Slack** - Join the Phoenix community for support
- **Example Notebooks** - Additional examples in the Phoenix cookbook

## 🐛 Troubleshooting

### Common Issues

1. **"OPENAI_API_KEY not found"**
   - Make sure you've set your OpenAI API key as an environment variable
   - Check that your API key is valid and has sufficient credits

2. **"Phoenix UI not loading"**
   - Ensure `setup_phoenix.py` is running
   - Check that port 6006 is not blocked by firewall
   - Try accessing `http://localhost:6006` directly

3. **"No traces found"**
   - Make sure you're running the tracing example after starting Phoenix
   - Check that OpenTelemetry instrumentation is properly configured

4. **Import errors**
   - Verify all dependencies are installed: `pip install -r requirements.txt`
   - Check Python version compatibility (3.8+)

### Getting Help

- Check the Phoenix documentation for detailed guides
- Join the community Slack for real-time support
- Review the example code comments for implementation details
- Open issues on the Phoenix GitHub repository

## 📝 License

This project is provided as educational examples for using Arize Phoenix. Please refer to the Phoenix project for licensing information.

---

**Happy Evaluating! 🎉**

Start with `python setup_phoenix.py` and then explore the examples to understand how Phoenix can help you build better LLM applications.
